{% extends 'layouts/base.html' %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-edit"></i> تعديل نوع إجازة</h2>
        <a href="{{ url_for('leaves.types') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة إلى أنواع الإجازات
        </a>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-edit"></i> بيانات نوع الإجازة</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('leaves.edit_type', id=leave_type.id) }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.name.label(class="form-label") }}
                            {% if form.name.errors %}
                                {{ form.name(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.name(class="form-control") }}
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {% if form.description.errors %}
                                {{ form.description(class="form-control is-invalid", rows=3) }}
                                <div class="invalid-feedback">
                                    {% for error in form.description.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.description(class="form-control", rows=3) }}
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.color.label(class="form-label") }}
                            {% if form.color.errors %}
                                {{ form.color(class="form-control is-invalid", type="color") }}
                                <div class="invalid-feedback">
                                    {% for error in form.color.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.color(class="form-control", type="color") }}
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('leaves.types') }}" class="btn btn-secondary me-md-2">إلغاء</a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
