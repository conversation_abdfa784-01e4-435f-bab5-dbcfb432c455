#!/usr/bin/env python3
"""
أداة تنظيف شاملة لنظام إدارة الموارد البشرية
"""
import os
import shutil
import glob
import json
from datetime import datetime, timedelta

class SystemCleaner:
    def __init__(self):
        self.cleaned_files = []
        self.cleaned_dirs = []
        self.errors = []
        self.total_size_freed = 0
        
    def clean_cache_files(self):
        """تنظيف ملفات التخزين المؤقت"""
        print("🗑️ تنظيف ملفات التخزين المؤقت...")
        
        # حذف ملفات __pycache__
        self._clean_pycache_recursive()
        
        # حذف ملفات .pyc
        self._clean_pyc_files()
        
        # حذف ملفات .pyo
        self._clean_pyo_files()
        
    def _clean_pycache_recursive(self):
        """حذف جميع مجلدات __pycache__ بشكل تكراري"""
        print("  🔄 حذف مجلدات __pycache__...")
        
        for root, dirs, files in os.walk('.'):
            if '__pycache__' in dirs:
                pycache_path = os.path.join(root, '__pycache__')
                try:
                    size = self._get_directory_size(pycache_path)
                    shutil.rmtree(pycache_path)
                    self.cleaned_dirs.append(pycache_path)
                    self.total_size_freed += size
                    print(f"    ✅ حذف: {pycache_path} ({self._format_size(size)})")
                except Exception as e:
                    error = f"فشل في حذف {pycache_path}: {e}"
                    self.errors.append(error)
                    print(f"    ❌ {error}")
    
    def _clean_pyc_files(self):
        """حذف ملفات .pyc"""
        print("  🔄 حذف ملفات .pyc...")
        
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith('.pyc'):
                    file_path = os.path.join(root, file)
                    try:
                        size = os.path.getsize(file_path)
                        os.remove(file_path)
                        self.cleaned_files.append(file_path)
                        self.total_size_freed += size
                        print(f"    ✅ حذف: {file_path}")
                    except Exception as e:
                        error = f"فشل في حذف {file_path}: {e}"
                        self.errors.append(error)
                        print(f"    ❌ {error}")
    
    def _clean_pyo_files(self):
        """حذف ملفات .pyo"""
        print("  🔄 حذف ملفات .pyo...")
        
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith('.pyo'):
                    file_path = os.path.join(root, file)
                    try:
                        size = os.path.getsize(file_path)
                        os.remove(file_path)
                        self.cleaned_files.append(file_path)
                        self.total_size_freed += size
                        print(f"    ✅ حذف: {file_path}")
                    except Exception as e:
                        error = f"فشل في حذف {file_path}: {e}"
                        self.errors.append(error)
                        print(f"    ❌ {error}")
    
    def clean_temp_files(self):
        """تنظيف الملفات المؤقتة"""
        print("\n🗂️ تنظيف الملفات المؤقتة...")
        
        temp_patterns = [
            '*.tmp', '*.temp', '*.bak', '*.swp', '*.swo',
            '*.orig', '*.rej', '*~', '.DS_Store', 'Thumbs.db'
        ]
        
        for pattern in temp_patterns:
            self._clean_files_by_pattern(pattern)
    
    def _clean_files_by_pattern(self, pattern):
        """حذف الملفات حسب النمط"""
        print(f"  🔍 البحث عن ملفات: {pattern}")
        
        for root, dirs, files in os.walk('.'):
            for file in files:
                if self._matches_pattern(file, pattern):
                    file_path = os.path.join(root, file)
                    try:
                        size = os.path.getsize(file_path)
                        os.remove(file_path)
                        self.cleaned_files.append(file_path)
                        self.total_size_freed += size
                        print(f"    ✅ حذف: {file_path}")
                    except Exception as e:
                        error = f"فشل في حذف {file_path}: {e}"
                        self.errors.append(error)
                        print(f"    ❌ {error}")
    
    def _matches_pattern(self, filename, pattern):
        """فحص تطابق اسم الملف مع النمط"""
        if pattern.startswith('*'):
            return filename.endswith(pattern[1:])
        elif pattern.endswith('*'):
            return filename.startswith(pattern[:-1])
        else:
            return filename == pattern
    
    def clean_old_databases(self):
        """تنظيف قواعد البيانات القديمة أو التالفة"""
        print("\n🗄️ تنظيف قواعد البيانات القديمة...")
        
        db_patterns = ['*.db', '*.sqlite', '*.sqlite3']
        
        for pattern in db_patterns:
            for root, dirs, files in os.walk('.'):
                for file in files:
                    if self._matches_pattern(file, pattern):
                        file_path = os.path.join(root, file)
                        self._check_and_clean_database(file_path)
    
    def _check_and_clean_database(self, db_path):
        """فحص وتنظيف قاعدة بيانات"""
        try:
            size = os.path.getsize(db_path)
            
            # فحص إذا كان الملف فارغ أو صغير جداً (أقل من 1KB)
            if size < 1024:
                os.remove(db_path)
                self.cleaned_files.append(db_path)
                self.total_size_freed += size
                print(f"    ✅ حذف قاعدة بيانات فارغة: {db_path}")
                return
            
            # فحص إذا كان الملف قديم جداً (أكثر من 30 يوم)
            mod_time = datetime.fromtimestamp(os.path.getmtime(db_path))
            if datetime.now() - mod_time > timedelta(days=30):
                # لا نحذف قواعد البيانات القديمة تلقائياً، فقط نبلغ عنها
                print(f"    ⚠️ قاعدة بيانات قديمة: {db_path} (آخر تعديل: {mod_time.strftime('%Y-%m-%d')})")
            
        except Exception as e:
            error = f"فشل في فحص {db_path}: {e}"
            self.errors.append(error)
            print(f"    ❌ {error}")
    
    def clean_log_files(self):
        """تنظيف ملفات السجلات القديمة"""
        print("\n📝 تنظيف ملفات السجلات القديمة...")
        
        log_patterns = ['*.log', '*.log.*']
        
        for pattern in log_patterns:
            for root, dirs, files in os.walk('.'):
                for file in files:
                    if self._matches_pattern(file, pattern):
                        file_path = os.path.join(root, file)
                        self._check_and_clean_log(file_path)
    
    def _check_and_clean_log(self, log_path):
        """فحص وتنظيف ملف سجل"""
        try:
            size = os.path.getsize(log_path)
            mod_time = datetime.fromtimestamp(os.path.getmtime(log_path))
            
            # حذف ملفات السجلات الأكبر من 10MB
            if size > 10 * 1024 * 1024:  # 10MB
                os.remove(log_path)
                self.cleaned_files.append(log_path)
                self.total_size_freed += size
                print(f"    ✅ حذف ملف سجل كبير: {log_path} ({self._format_size(size)})")
                return
            
            # حذف ملفات السجلات القديمة (أكثر من 7 أيام)
            if datetime.now() - mod_time > timedelta(days=7):
                os.remove(log_path)
                self.cleaned_files.append(log_path)
                self.total_size_freed += size
                print(f"    ✅ حذف ملف سجل قديم: {log_path}")
                return
                
        except Exception as e:
            error = f"فشل في فحص {log_path}: {e}"
            self.errors.append(error)
            print(f"    ❌ {error}")
    
    def clean_upload_files(self):
        """تنظيف ملفات الرفع غير المرتبطة"""
        print("\n📁 فحص ملفات الرفع...")
        
        upload_dir = 'hrmsystem/application/static/uploads'
        if not os.path.exists(upload_dir):
            print("    ℹ️ مجلد الرفع غير موجود")
            return
        
        # هذا يتطلب فحص قاعدة البيانات للتأكد من الملفات المرتبطة
        # سنكتفي بعرض معلومات عن الملفات الموجودة
        try:
            files = os.listdir(upload_dir)
            total_size = 0
            for file in files:
                file_path = os.path.join(upload_dir, file)
                if os.path.isfile(file_path):
                    total_size += os.path.getsize(file_path)
            
            print(f"    ℹ️ عدد ملفات الرفع: {len(files)}")
            print(f"    ℹ️ الحجم الإجمالي: {self._format_size(total_size)}")
            
        except Exception as e:
            error = f"فشل في فحص مجلد الرفع: {e}"
            self.errors.append(error)
            print(f"    ❌ {error}")
    
    def clean_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        print("\n💾 تنظيف النسخ الاحتياطية القديمة...")
        
        backup_dirs = []
        for item in os.listdir('.'):
            if item.startswith('backup_') and os.path.isdir(item):
                backup_dirs.append(item)
        
        if not backup_dirs:
            print("    ℹ️ لا توجد نسخ احتياطية")
            return
        
        # ترتيب النسخ الاحتياطية حسب التاريخ
        backup_dirs.sort(reverse=True)
        
        # الاحتفاظ بآخر 3 نسخ احتياطية فقط
        if len(backup_dirs) > 3:
            for backup_dir in backup_dirs[3:]:
                try:
                    size = self._get_directory_size(backup_dir)
                    shutil.rmtree(backup_dir)
                    self.cleaned_dirs.append(backup_dir)
                    self.total_size_freed += size
                    print(f"    ✅ حذف نسخة احتياطية قديمة: {backup_dir} ({self._format_size(size)})")
                except Exception as e:
                    error = f"فشل في حذف {backup_dir}: {e}"
                    self.errors.append(error)
                    print(f"    ❌ {error}")
        else:
            print(f"    ℹ️ عدد النسخ الاحتياطية: {len(backup_dirs)} (ضمن الحد المسموح)")
    
    def _get_directory_size(self, directory):
        """حساب حجم المجلد"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        total_size += os.path.getsize(filepath)
        except Exception:
            pass
        return total_size
    
    def _format_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f}{size_names[i]}"
    
    def generate_cleanup_report(self):
        """إنشاء تقرير التنظيف"""
        print("\n📊 إنشاء تقرير التنظيف...")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'cleaned_files': self.cleaned_files,
            'cleaned_directories': self.cleaned_dirs,
            'errors': self.errors,
            'total_size_freed': self.total_size_freed,
            'summary': {
                'files_cleaned': len(self.cleaned_files),
                'directories_cleaned': len(self.cleaned_dirs),
                'errors_count': len(self.errors),
                'size_freed_formatted': self._format_size(self.total_size_freed)
            }
        }
        
        # حفظ التقرير
        report_file = f"cleanup_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # إنشاء تقرير نصي
        text_report = self._generate_text_cleanup_report(report)
        text_report_file = f"cleanup_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(text_report_file, 'w', encoding='utf-8') as f:
            f.write(text_report)
        
        print(f"✅ تم إنشاء تقرير التنظيف: {report_file}")
        print(f"✅ تم إنشاء التقرير النصي: {text_report_file}")
        
        return report_file, text_report_file
    
    def _generate_text_cleanup_report(self, report):
        """إنشاء تقرير نصي للتنظيف"""
        text = f"""
🧹 تقرير تنظيف نظام إدارة الموارد البشرية
═══════════════════════════════════════════════════════════

📅 التاريخ والوقت: {report['timestamp']}

📊 ملخص النتائج:
═══════════════════════════════════════════════════════════
• الملفات المحذوفة: {report['summary']['files_cleaned']}
• المجلدات المحذوفة: {report['summary']['directories_cleaned']}
• المساحة المحررة: {report['summary']['size_freed_formatted']}
• الأخطاء: {report['summary']['errors_count']}

"""
        
        if report['cleaned_files']:
            text += "\n✅ الملفات المحذوفة:\n"
            text += "═" * 50 + "\n"
            for i, file in enumerate(report['cleaned_files'], 1):
                text += f"{i}. {file}\n"
        
        if report['cleaned_directories']:
            text += "\n📁 المجلدات المحذوفة:\n"
            text += "═" * 50 + "\n"
            for i, directory in enumerate(report['cleaned_directories'], 1):
                text += f"{i}. {directory}\n"
        
        if report['errors']:
            text += "\n❌ الأخطاء:\n"
            text += "═" * 50 + "\n"
            for i, error in enumerate(report['errors'], 1):
                text += f"{i}. {error}\n"
        
        text += "\n🎯 التوصيات:\n"
        text += "═" * 50 + "\n"
        text += "• تشغيل التنظيف بشكل دوري (أسبوعياً)\n"
        text += "• مراقبة حجم ملفات السجلات\n"
        text += "• إنشاء نسخ احتياطية قبل التنظيف\n"
        text += "• فحص الملفات المحذوفة للتأكد من عدم الحاجة إليها\n"
        
        return text

def main():
    """الدالة الرئيسية"""
    print("🧹 أداة التنظيف الشاملة لنظام إدارة الموارد البشرية")
    print("=" * 70)
    
    cleaner = SystemCleaner()
    
    # تنظيف ملفات التخزين المؤقت
    cleaner.clean_cache_files()
    
    # تنظيف الملفات المؤقتة
    cleaner.clean_temp_files()
    
    # تنظيف قواعد البيانات القديمة
    cleaner.clean_old_databases()
    
    # تنظيف ملفات السجلات
    cleaner.clean_log_files()
    
    # فحص ملفات الرفع
    cleaner.clean_upload_files()
    
    # تنظيف النسخ الاحتياطية القديمة
    cleaner.clean_old_backups()
    
    # إنشاء التقرير
    report_file, text_report_file = cleaner.generate_cleanup_report()
    
    # عرض النتائج النهائية
    print("\n" + "=" * 70)
    print("🎉 تم الانتهاء من التنظيف!")
    print("=" * 70)
    print(f"📊 النتائج:")
    print(f"   • الملفات المحذوفة: {len(cleaner.cleaned_files)}")
    print(f"   • المجلدات المحذوفة: {len(cleaner.cleaned_dirs)}")
    print(f"   • المساحة المحررة: {cleaner._format_size(cleaner.total_size_freed)}")
    print(f"   • الأخطاء: {len(cleaner.errors)}")
    print(f"   • التقرير: {text_report_file}")

if __name__ == '__main__':
    main()
