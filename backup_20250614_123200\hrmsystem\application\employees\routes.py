from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from ..models import Employee, EmployeeCategory, EmployeeStatus, AuditLog, Permission
from .. import db
from .forms import EmployeeForm, EmployeeSearchForm, ImportEmployeesForm
from ..utilities import save_picture, generate_qr_code, generate_employee_qr_code
from ..decorators import permission_required
from flask import current_app
import os
import pandas as pd
from datetime import datetime
import uuid

employees_bp = Blueprint('employees', __name__)

@employees_bp.route('/')
@login_required
@permission_required(Permission.VIEW_EMPLOYEES)
def index():
    form = EmployeeSearchForm()
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config['EMPLOYEES_PER_PAGE']

    # Get search parameters from request args
    search = request.args.get('search', '')
    status = request.args.get('status', '')
    category = request.args.get('category', '')
    unit = request.args.get('unit', '')
    report = request.args.get('report', '')

    # Build query
    query = Employee.query

    if search:
        query = query.filter(
            (Employee.name.ilike(f'%{search}%')) |
            (Employee.military_id.ilike(f'%{search}%')) |
            (Employee.national_id.ilike(f'%{search}%'))
        )

    if status:
        query = query.filter(Employee.status == status)

    if category:
        query = query.filter(Employee.category == category)

    if unit:
        query = query.filter(Employee.unit.ilike(f'%{unit}%'))

    # Order by name
    query = query.order_by(Employee.name)

    # Check if this is a report request
    if report:
        # Redirect to the reports blueprint
        return redirect(url_for('reports.generate_report', report_type=report, search=search, status=status, category=category, unit=unit))

    # Paginate results for normal view
    employees = query.paginate(page=page, per_page=per_page)

    return render_template('employees/index.html',
                           employees=employees,
                           form=form,
                           search=search,
                           status=status,
                           category=category,
                           unit=unit,
                           title='إدارة الموظفين')

@employees_bp.route('/create', methods=['GET', 'POST'])
@login_required
@permission_required(Permission.ADD_EMPLOYEE)
def create():
    form = EmployeeForm()

    if form.validate_on_submit():
        # Handle profile image
        profile_image = None
        if form.profile_image.data:
            profile_image = save_picture(form.profile_image.data, folder='employees')

        # Create new employee
        employee = Employee(
            military_id=form.military_id.data,
            name=form.name.data,
            national_id=form.national_id.data,
            blood_type=form.blood_type.data,
            date_of_birth=form.date_of_birth.data,
            birth_place=form.birth_place.data,
            current_residence=form.current_residence.data,
            education=form.education.data,
            education_date=form.education_date.data,
            category=form.category.data if form.category.data else None,
            bank_name=form.bank_name.data,
            bank_account=form.bank_account.data,
            military_rank=form.military_rank.data,
            unit=form.unit.data,
            position=form.position.data,
            status=form.status.data,
            status_notes=form.status_notes.data,
            phone=form.phone.data,
            email=form.email.data,
            profile_image=profile_image,
            leave_balance=form.leave_balance.data if form.leave_balance.data else 0,
            hire_date=form.hire_date.data,
            last_promotion_date=form.last_promotion_date.data
        )

        db.session.add(employee)

        # Create audit log
        log = AuditLog(
            user_id=current_user.id,
            action='create',
            entity='Employee',
            entity_id=employee.id,
            details=f'تم إنشاء الموظف {employee.name}',
            ip_address=request.remote_addr
        )
        db.session.add(log)

        db.session.commit()
        flash(f'تم إنشاء الموظف {form.name.data} بنجاح!', 'success')
        return redirect(url_for('employees.index'))

    return render_template('employees/create.html', form=form, title='إضافة موظف جديد')

@employees_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@permission_required(Permission.EDIT_EMPLOYEE)
def edit(id):
    employee = Employee.query.get_or_404(id)
    form = EmployeeForm(obj=employee)
    # Store the employee ID for validation
    form.employee_id = id

    if form.validate_on_submit():
        # Handle profile image
        if form.profile_image.data and hasattr(form.profile_image.data, 'filename'):
            profile_image = save_picture(form.profile_image.data, folder='employees')
            employee.profile_image = profile_image

        # Update employee data
        employee.military_id = form.military_id.data
        employee.name = form.name.data
        employee.national_id = form.national_id.data
        employee.blood_type = form.blood_type.data
        employee.date_of_birth = form.date_of_birth.data
        employee.birth_place = form.birth_place.data
        employee.current_residence = form.current_residence.data
        employee.education = form.education.data
        employee.education_date = form.education_date.data
        employee.category = form.category.data if form.category.data else None
        employee.bank_name = form.bank_name.data
        employee.bank_account = form.bank_account.data
        employee.military_rank = form.military_rank.data
        employee.unit = form.unit.data
        employee.position = form.position.data
        employee.status = form.status.data
        employee.status_notes = form.status_notes.data
        employee.phone = form.phone.data
        employee.email = form.email.data
        employee.leave_balance = form.leave_balance.data if form.leave_balance.data else 0
        employee.hire_date = form.hire_date.data
        employee.last_promotion_date = form.last_promotion_date.data

        # Create audit log
        log = AuditLog(
            user_id=current_user.id,
            action='update',
            entity='Employee',
            entity_id=employee.id,
            details=f'تم تحديث بيانات الموظف {employee.name}',
            ip_address=request.remote_addr
        )
        db.session.add(log)

        db.session.commit()
        flash(f'تم تحديث بيانات الموظف {form.name.data} بنجاح!', 'success')
        return redirect(url_for('employees.view', id=employee.id))

    return render_template('employees/edit.html', form=form, employee=employee, title='تعديل بيانات موظف')

@employees_bp.route('/view/<int:id>')
@login_required
@permission_required(Permission.VIEW_EMPLOYEES)
def view(id):
    employee = Employee.query.get_or_404(id)
    try:
        # استخدام دالة generate_employee_qr_code لإنشاء رمز QR يحتوي على بيانات الموظف مباشرة
        qr_code = generate_employee_qr_code(employee, size=200)
    except Exception as e:
        print(f"Error generating QR code: {e}")
        # If there's an error generating the QR code, use a default image
        qr_code = url_for('static', filename='img/default_qr.png')
    return render_template('employees/view.html', employee=employee, qr_code=qr_code, title=f'بيانات الموظف: {employee.name}')

@employees_bp.route('/qr/<int:id>')
def qr_view(id):
    employee = Employee.query.get_or_404(id)
    return render_template('employees/qr_view.html', employee=employee, title=f'بيانات الموظف: {employee.name}')

@employees_bp.route('/delete/<int:id>', methods=['POST'])
@login_required
@permission_required(Permission.DELETE_EMPLOYEE)
def delete(id):
    employee = Employee.query.get_or_404(id)

    # Create audit log
    log = AuditLog(
        user_id=current_user.id,
        action='delete',
        entity='Employee',
        entity_id=employee.id,
        details=f'تم حذف الموظف {employee.name}',
        ip_address=request.remote_addr
    )
    db.session.add(log)

    db.session.delete(employee)
    db.session.commit()
    flash(f'تم حذف الموظف {employee.name} بنجاح!', 'success')
    return redirect(url_for('employees.index'))

@employees_bp.route('/print/<int:id>')
@login_required
@permission_required(Permission.PRINT_EMPLOYEE)
def print_employee(id):
    employee = Employee.query.get_or_404(id)
    try:
        # Generate QR code with employee data directly
        qr_code = generate_employee_qr_code(employee, size=150)
    except Exception as e:
        print(f"Error generating QR code: {e}")
        # If there's an error generating the QR code, use a default image
        qr_code = url_for('static', filename='img/default_qr.png')
    return render_template('employees/print.html', employee=employee, qr_code=qr_code, now=datetime.now(), current_user=current_user, title=f'طباعة بيانات الموظف: {employee.name}')

@employees_bp.route('/export_data/<int:id>')
@login_required
@permission_required(Permission.PRINT_EMPLOYEE)
def export_data(id):
    """Export employee data to Excel"""
    employee = Employee.query.get_or_404(id)

    # Create a pandas DataFrame for the Excel file
    data = {
        'البيانات الشخصية': [
            ['الاسم', employee.name],
            ['الرقم العسكري', employee.military_id],
            ['الرقم الوطني', employee.national_id or 'غير محدد'],
            ['الرتبة', employee.military_rank or 'غير محدد'],
            ['تاريخ الميلاد', employee.date_of_birth.strftime('%Y-%m-%d') if employee.date_of_birth else 'غير محدد'],
            ['مكان الميلاد', employee.birth_place or 'غير محدد'],
            ['فصيلة الدم', employee.blood_type or 'غير محدد'],
            ['السكن الحالي', employee.current_residence or 'غير محدد']
        ],
        'البيانات التعليمية والمهنية': [
            ['المؤهل العلمي', employee.education or 'غير محدد'],
            ['تاريخ المؤهل', employee.education_date.strftime('%Y-%m-%d') if employee.education_date else 'غير محدد'],
            ['الفئة', employee.category.value if employee.category else 'غير محدد'],
            ['الوحدة', employee.unit],
            ['العمل المكلف به', employee.position],
            ['الحالة', employee.status.value if employee.status else 'غير محدد'],
            ['تاريخ التعيين', employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else 'غير محدد'],
            ['تاريخ آخر ترقية', employee.last_promotion_date.strftime('%Y-%m-%d') if employee.last_promotion_date else 'غير محدد']
        ],
        'بيانات الاتصال والبيانات المالية': [
            ['رقم الهاتف', employee.phone or 'غير محدد'],
            ['البريد الإلكتروني', employee.email or 'غير محدد'],
            ['المصرف', employee.bank_name or 'غير محدد'],
            ['رقم الحساب المصرفي', employee.bank_account or 'غير محدد']
        ],
        'بيانات الإجازات': [
            ['رصيد الإجازة', f"{employee.leave_balance} يوم"],
            ['ملاحظات الحالة', employee.status_notes or 'غير محدد']
        ]
    }

    # Create Excel file in memory
    import pandas as pd
    import io
    from flask import send_file

    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        # Write each section to a separate sheet
        for sheet_name, sheet_data in data.items():
            df = pd.DataFrame(sheet_data, columns=['البيان', 'القيمة'])
            df.to_excel(writer, sheet_name=sheet_name, index=False)

            # Auto-adjust columns' width
            worksheet = writer.sheets[sheet_name]
            for i, col in enumerate(df.columns):
                max_length = max(df[col].astype(str).map(len).max(), len(col)) + 2
                worksheet.column_dimensions[chr(65 + i)].width = max_length

    output.seek(0)

    # Generate a unique filename
    filename = f"employee_{employee.military_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"

    return send_file(
        output,
        as_attachment=True,
        download_name=filename,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

@employees_bp.route('/import', methods=['GET', 'POST'])
@login_required
@permission_required(Permission.IMPORT_EMPLOYEES)
def import_employees():
    form = ImportEmployeesForm()

    if form.validate_on_submit():
        # Save the uploaded file
        file = form.file.data
        filename = secure_filename(file.filename)
        temp_path = os.path.join(current_app.config['UPLOAD_FOLDER'], f"temp_{uuid.uuid4()}_{filename}")
        os.makedirs(os.path.dirname(temp_path), exist_ok=True)
        file.save(temp_path)

        try:
            # Read Excel file
            df = pd.read_excel(temp_path)

            # Check required columns
            required_columns = ['الاسم', 'الرقم العسكري', 'الوحدة', 'العمل المكلف به']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                flash(f'الملف لا يحتوي على الأعمدة المطلوبة: {", ".join(missing_columns)}', 'danger')
                return redirect(url_for('employees.import_employees'))

            # Process data
            success_count = 0
            error_count = 0
            errors = []

            for index, row in df.iterrows():
                try:
                    # Check if employee exists by military_id
                    if pd.isna(row['الرقم العسكري']):
                        raise ValueError('الرقم العسكري مطلوب')

                    military_id = str(row['الرقم العسكري'])
                    print(f"Processing row {index+1}: {row['الاسم']} (ID: {military_id})")

                    existing_employee = Employee.query.filter_by(military_id=military_id).first()

                    # If national_id is provided, check if it's already in use by another employee
                    national_id = None
                    if 'الرقم الوطني' in df.columns and not pd.isna(row['الرقم الوطني']):
                        national_id = str(row['الرقم الوطني'])
                        existing_by_national_id = Employee.query.filter_by(national_id=national_id).first()
                        if existing_by_national_id and (not existing_employee or existing_by_national_id.id != existing_employee.id):
                            # Instead of skipping, make the national_id unique by appending a timestamp
                            print(f"National ID {national_id} already exists for employee {existing_by_national_id.name}, making it unique")
                            national_id = f"{national_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

                    if existing_employee and form.update_existing.data:
                        # Update existing employee
                        existing_employee.name = row['الاسم']
                        existing_employee.unit = row['الوحدة']
                        existing_employee.position = row['العمل المكلف به']

                        # Use the possibly modified national_id value
                        if national_id:
                            existing_employee.national_id = national_id

                        if 'الرتبة' in df.columns and not pd.isna(row['الرتبة']):
                            existing_employee.military_rank = row['الرتبة']

                        if 'الفئة' in df.columns and not pd.isna(row['الفئة']):
                            category_map = {
                                'ضباط': EmployeeCategory.OFFICER.name,
                                'ضباط صف': EmployeeCategory.NCO.name,
                                'موظف': EmployeeCategory.EMPLOYEE.name
                            }
                            existing_employee.category = category_map.get(row['الفئة'])

                        if 'الحالة' in df.columns and not pd.isna(row['الحالة']):
                            status_map = {
                                'مستمر': EmployeeStatus.ACTIVE.name,
                                'غائب/هارب': EmployeeStatus.ABSENT.name,
                                'منتدب': EmployeeStatus.ASSIGNED.name,
                                'في إجازة': EmployeeStatus.ON_LEAVE.name,
                                'موقوف': EmployeeStatus.SUSPENDED.name,
                                'متفرق': EmployeeStatus.SCATTERED.name,
                                'عيادة طبية': EmployeeStatus.MEDICAL.name
                            }
                            existing_employee.status = status_map.get(row['الحالة'], EmployeeStatus.ACTIVE.name)

                        if 'ملاحظات الحالة' in df.columns and not pd.isna(row['ملاحظات الحالة']):
                            existing_employee.status_notes = row['ملاحظات الحالة']

                        if 'رقم الهاتف' in df.columns and not pd.isna(row['رقم الهاتف']):
                            existing_employee.phone = str(row['رقم الهاتف'])

                        if 'البريد الإلكتروني' in df.columns and not pd.isna(row['البريد الإلكتروني']):
                            existing_employee.email = row['البريد الإلكتروني']

                        if 'فصيلة الدم' in df.columns and not pd.isna(row['فصيلة الدم']):
                            existing_employee.blood_type = row['فصيلة الدم']

                        if 'تاريخ الميلاد' in df.columns and not pd.isna(row['تاريخ الميلاد']):
                            try:
                                birth_date = pd.to_datetime(row['تاريخ الميلاد'], errors='coerce')
                                if birth_date is not pd.NaT:
                                    existing_employee.date_of_birth = birth_date.date()
                            except Exception as e:
                                print(f"Error converting birth date: {e}")

                        if 'مكان الميلاد' in df.columns and not pd.isna(row['مكان الميلاد']):
                            existing_employee.birth_place = row['مكان الميلاد']

                        if 'السكن الحالي' in df.columns and not pd.isna(row['السكن الحالي']):
                            existing_employee.current_residence = row['السكن الحالي']

                        if 'المؤهل العلمي' in df.columns and not pd.isna(row['المؤهل العلمي']):
                            existing_employee.education = row['المؤهل العلمي']

                        if 'تاريخ المؤهل' in df.columns and not pd.isna(row['تاريخ المؤهل']):
                            try:
                                education_date = pd.to_datetime(row['تاريخ المؤهل'], errors='coerce')
                                if education_date is not pd.NaT:
                                    existing_employee.education_date = education_date.date()
                            except Exception as e:
                                print(f"Error converting education date: {e}")

                        if 'المصرف' in df.columns and not pd.isna(row['المصرف']):
                            existing_employee.bank_name = row['المصرف']

                        if 'رقم الحساب المصرفي' in df.columns and not pd.isna(row['رقم الحساب المصرفي']):
                            existing_employee.bank_account = str(row['رقم الحساب المصرفي'])

                        if 'رصيد الإجازة' in df.columns and not pd.isna(row['رصيد الإجازة']):
                            existing_employee.leave_balance = int(row['رصيد الإجازة'])

                        if 'تاريخ التعيين' in df.columns and not pd.isna(row['تاريخ التعيين']):
                            try:
                                hire_date = pd.to_datetime(row['تاريخ التعيين'], errors='coerce')
                                if hire_date is not pd.NaT:
                                    existing_employee.hire_date = hire_date.date()
                            except Exception as e:
                                print(f"Error converting hire date: {e}")

                        if 'تاريخ آخر ترقية' in df.columns and not pd.isna(row['تاريخ آخر ترقية']):
                            try:
                                promotion_date = pd.to_datetime(row['تاريخ آخر ترقية'], errors='coerce')
                                if promotion_date is not pd.NaT:
                                    existing_employee.last_promotion_date = promotion_date.date()
                            except Exception as e:
                                print(f"Error converting promotion date: {e}")

                        success_count += 1

                        # Create audit log
                        log = AuditLog(
                            user_id=current_user.id,
                            action='update',
                            entity='Employee',
                            entity_id=existing_employee.id,
                            details=f'تم تحديث بيانات الموظف {existing_employee.name} من خلال الاستيراد',
                            ip_address=request.remote_addr
                        )
                        db.session.add(log)

                    elif not existing_employee:
                        # Handle military_id to avoid unique constraint violation
                        military_id_value = str(row['الرقم العسكري'])  # Convert to string to ensure consistency
                        # Check if military_id already exists (should not happen as we already checked, but just to be safe)
                        existing_military_id = Employee.query.filter_by(military_id=military_id_value).first()
                        if existing_military_id:
                            # Generate a unique military_id by appending a timestamp
                            military_id_value = f"{military_id_value}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

                        # Create new employee with default values for required fields
                        new_employee = Employee(
                            name=row['الاسم'],
                            military_id=military_id_value,
                            unit=row['الوحدة'],  # Default value already set if missing
                            position=row['العمل المكلف به'],  # Default value already set if missing
                            national_id=national_id,
                            blood_type=row['فصيلة الدم'] if 'فصيلة الدم' in df.columns and not pd.isna(row['فصيلة الدم']) else None,
                            date_of_birth=pd.to_datetime(row['تاريخ الميلاد'], errors='coerce').date() if 'تاريخ الميلاد' in df.columns and not pd.isna(row['تاريخ الميلاد']) and pd.to_datetime(row['تاريخ الميلاد'], errors='coerce') is not pd.NaT else None,
                            birth_place=row['مكان الميلاد'] if 'مكان الميلاد' in df.columns and not pd.isna(row['مكان الميلاد']) else None,
                            current_residence=row['السكن الحالي'] if 'السكن الحالي' in df.columns and not pd.isna(row['السكن الحالي']) else None,
                            education=row['المؤهل العلمي'] if 'المؤهل العلمي' in df.columns and not pd.isna(row['المؤهل العلمي']) else None,
                            education_date=pd.to_datetime(row['تاريخ المؤهل'], errors='coerce').date() if 'تاريخ المؤهل' in df.columns and not pd.isna(row['تاريخ المؤهل']) and pd.to_datetime(row['تاريخ المؤهل'], errors='coerce') is not pd.NaT else None,
                            bank_name=row['المصرف'] if 'المصرف' in df.columns and not pd.isna(row['المصرف']) else None,
                            bank_account=str(row['رقم الحساب المصرفي']) if 'رقم الحساب المصرفي' in df.columns and not pd.isna(row['رقم الحساب المصرفي']) else None,
                            military_rank=row['الرتبة'] if 'الرتبة' in df.columns and not pd.isna(row['الرتبة']) else None,
                            phone=str(row['رقم الهاتف']) if 'رقم الهاتف' in df.columns and not pd.isna(row['رقم الهاتف']) else None,
                            email=row['البريد الإلكتروني'] if 'البريد الإلكتروني' in df.columns and not pd.isna(row['البريد الإلكتروني']) else None,
                            leave_balance=int(row['رصيد الإجازة']) if 'رصيد الإجازة' in df.columns and not pd.isna(row['رصيد الإجازة']) else 0,
                            hire_date=pd.to_datetime(row['تاريخ التعيين'], errors='coerce').date() if 'تاريخ التعيين' in df.columns and not pd.isna(row['تاريخ التعيين']) and pd.to_datetime(row['تاريخ التعيين'], errors='coerce') is not pd.NaT else None,
                            last_promotion_date=pd.to_datetime(row['تاريخ آخر ترقية'], errors='coerce').date() if 'تاريخ آخر ترقية' in df.columns and not pd.isna(row['تاريخ آخر ترقية']) and pd.to_datetime(row['تاريخ آخر ترقية'], errors='coerce') is not pd.NaT else None
                        )

                        # Set category if provided
                        if 'الفئة' in df.columns and not pd.isna(row['الفئة']):
                            category_map = {
                                'ضباط': EmployeeCategory.OFFICER.name,
                                'ضباط صف': EmployeeCategory.NCO.name,
                                'موظف': EmployeeCategory.EMPLOYEE.name
                            }
                            new_employee.category = category_map.get(row['الفئة'])

                        # Set status if provided
                        if 'الحالة' in df.columns and not pd.isna(row['الحالة']):
                            status_map = {
                                'مستمر': EmployeeStatus.ACTIVE.name,
                                'غائب/هارب': EmployeeStatus.ABSENT.name,
                                'منتدب': EmployeeStatus.ASSIGNED.name,
                                'في إجازة': EmployeeStatus.ON_LEAVE.name,
                                'موقوف': EmployeeStatus.SUSPENDED.name,
                                'متفرق': EmployeeStatus.SCATTERED.name,
                                'عيادة طبية': EmployeeStatus.MEDICAL.name
                            }
                            new_employee.status = status_map.get(row['الحالة'], EmployeeStatus.ACTIVE.name)

                        # Set status notes if provided
                        if 'ملاحظات الحالة' in df.columns and not pd.isna(row['ملاحظات الحالة']):
                            new_employee.status_notes = row['ملاحظات الحالة']

                        db.session.add(new_employee)
                        # Flush to get the ID
                        db.session.flush()
                        success_count += 1

                        # Create audit log
                        log = AuditLog(
                            user_id=current_user.id,
                            action='create',
                            entity='Employee',
                            entity_id=new_employee.id,
                            details=f'تم إنشاء الموظف {new_employee.name} من خلال الاستيراد',
                            ip_address=request.remote_addr
                        )
                        db.session.add(log)

                    else:
                        # Employee exists but update_existing is False
                        error_count += 1
                        errors.append(f'الموظف {row["الاسم"]} (الرقم العسكري: {military_id}) موجود بالفعل ولم يتم تحديثه')

                except Exception as e:
                    error_count += 1
                    errors.append(f'خطأ في الصف {index+1}: {str(e)}')

            try:
                # Try to commit the changes
                db.session.commit()
                print("Successfully committed changes to database")
            except Exception as e:
                # If there's an error, rollback the transaction
                db.session.rollback()
                print(f"Error during commit: {str(e)}")
                flash(f'حدث خطأ أثناء حفظ البيانات: {str(e)}', 'danger')
                return redirect(url_for('employees.import_employees'))

            # Delete temporary file
            if os.path.exists(temp_path):
                os.remove(temp_path)

            # Show results
            if success_count > 0:
                flash(f'تم استيراد {success_count} موظف بنجاح!', 'success')

            if error_count > 0:
                flash(f'حدث {error_count} خطأ أثناء الاستيراد.', 'warning')
                for error in errors[:10]:  # Show only first 10 errors
                    flash(error, 'warning')
                if len(errors) > 10:
                    flash(f'... و {len(errors) - 10} أخطاء أخرى', 'warning')

            return redirect(url_for('employees.index'))

        except Exception as e:
            flash(f'حدث خطأ أثناء قراءة الملف: {str(e)}', 'danger')
            return redirect(url_for('employees.import_employees'))

    return render_template('employees/import.html', form=form, title='استيراد بيانات الموظفين')

@employees_bp.route('/export')
@login_required
def export_employees():
    # Get filter parameters
    search = request.args.get('search', '')
    status = request.args.get('status', '')
    category = request.args.get('category', '')
    unit = request.args.get('unit', '')

    # Build query
    query = Employee.query

    if search:
        query = query.filter(
            (Employee.name.ilike(f'%{search}%')) |
            (Employee.military_id.ilike(f'%{search}%')) |
            (Employee.national_id.ilike(f'%{search}%'))
        )

    if status:
        query = query.filter(Employee.status == status)

    if category:
        query = query.filter(Employee.category == category)

    if unit:
        query = query.filter(Employee.unit.ilike(f'%{unit}%'))

    # Order by name
    query = query.order_by(Employee.name)

    # Get all employees
    employees = query.all()

    # Create DataFrame
    data = []
    for employee in employees:
        data.append({
            'الاسم': employee.name,
            'الرقم العسكري': employee.military_id,
            'الرقم الوطني': employee.national_id,
            'فصيلة الدم': employee.blood_type,
            'تاريخ الميلاد': employee.date_of_birth,
            'مكان الميلاد': employee.birth_place,
            'السكن الحالي': employee.current_residence,
            'المؤهل العلمي': employee.education,
            'تاريخ المؤهل': employee.education_date,
            'الفئة': employee.category.value if employee.category else '',
            'المصرف': employee.bank_name,
            'رقم الحساب المصرفي': employee.bank_account,
            'الرتبة': employee.military_rank,
            'الوحدة': employee.unit,
            'العمل المكلف به': employee.position,
            'الحالة': employee.status.value if employee.status else '',
            'ملاحظات الحالة': employee.status_notes,
            'رقم الهاتف': employee.phone,
            'البريد الإلكتروني': employee.email,
            'رصيد الإجازة': employee.leave_balance,
            'تاريخ التعيين': employee.hire_date,
            'تاريخ آخر ترقية': employee.last_promotion_date
        })

    df = pd.DataFrame(data)

    # Create Excel file
    filename = f"employees_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    output_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # Write to Excel
    df.to_excel(output_path, index=False)

    # Create audit log
    log = AuditLog(
        user_id=current_user.id,
        action='export',
        entity='Employee',
        details=f'تم تصدير بيانات {len(employees)} موظف',
        ip_address=request.remote_addr
    )
    db.session.add(log)
    db.session.commit()

    # Return file for download using send_file
    from flask import send_file
    return send_file(output_path, as_attachment=True, download_name=filename)

@employees_bp.route('/get_units')
@login_required
def get_units():
    units = db.session.query(Employee.unit).distinct().all()
    return jsonify([unit[0] for unit in units if unit[0]])

@employees_bp.route('/get_ranks')
@login_required
def get_ranks():
    ranks = db.session.query(Employee.military_rank).distinct().all()
    return jsonify([rank[0] for rank in ranks if rank[0]])
