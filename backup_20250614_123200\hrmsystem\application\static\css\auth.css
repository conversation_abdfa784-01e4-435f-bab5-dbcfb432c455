/* Auth CSS file for HRM System */

/* Custom fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/* General styles */
body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fc;
    background-image: url('../img/bg-auth.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 100vh;
}

/* Card styles */
.card {
    border-radius: 1rem;
    border: none;
    box-shadow: 0 0.5rem 2rem 0 rgba(0, 0, 0, 0.2);
}

/* Form styles */
.form-control {
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
}

.form-control:focus {
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

.btn {
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
}

/* Logo styles */
.logo {
    max-width: 150px;
    margin-bottom: 1.5rem;
}

/* Animation */
.card {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
