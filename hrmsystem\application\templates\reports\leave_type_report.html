{% extends 'layouts/base_print.html' %}

{% block title %}
تقرير الإجازات حسب النوع
{% endblock %}

{% block content %}
    <!-- أزرار التحكم (تظهر فقط في الشاشة وليس عند الطباعة) -->
    <div class="control-buttons d-flex justify-content-between mb-4 no-print">
        <div>
            <button class="btn-print">
                <i class="fas fa-print me-1"></i> طباعة
            </button>
            <a href="{{ url_for('leaves.export_report', report_type='type', **request.args) }}" class="btn-export">
                <i class="fas fa-file-excel me-1"></i> تصدير Excel
            </a>
        </div>
        <button class="btn-back">
            <i class="fas fa-arrow-right me-1"></i> رجوع
        </button>
    </div>

    <!-- رأس التقرير -->
    <div class="report-header">
        <div class="report-title">
            كشف الإجازات حسب النوع
        </div>
        <div class="report-subtitle">
            {{ unit_name|default('قسم المرور والتراخيص الخمس') }}
        </div>
    </div>

    {% for type_name, leaves in report_data.items() %}
    <div class="report-subtitle mb-3">
        {{ type_name }} ({{ leaves|length }})
    </div>

    <!-- جدول التقرير -->
    <table class="report-table report-leave">
        <thead>
            <tr>
                <th width="5%">م</th>
                <th width="15%">الرقم العسكري</th>
                <th width="15%">الرتبة</th>
                <th width="30%">الاسم</th>
                <th width="15%">من تاريخ</th>
                <th width="15%">إلى تاريخ</th>
                <th width="5%">الأيام</th>
            </tr>
        </thead>
        <tbody>
            {% if leaves %}
                {% for leave in leaves %}
                <tr>
                    <td class="serial-number">{{ loop.index }}</td>
                    <td>{{ leave.employee.military_id }}</td>
                    <td>{{ leave.employee.military_rank }}</td>
                    <td style="text-align: right; font-weight: bold;">{{ leave.employee.name }}</td>
                    <td>{{ leave.start_date.strftime('%Y/%m/%d') }}</td>
                    <td>{{ leave.end_date.strftime('%Y/%m/%d') }}</td>
                    <td>{{ leave.total_days }}</td>
                </tr>
                {% endfor %}
            {% else %}
                {% for i in range(1, 11) %}
                <tr>
                    <td class="serial-number">{{ i }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                {% endfor %}
            {% endif %}
        </tbody>
    </table>
    {% endfor %}

    <!-- تذييل التقرير -->
    <div class="report-footer">
        <div>
            <p style="font-weight: bold; font-size: 16pt;">يعتمد</p>
            <div style="margin-top: 40px; border-top: 1px solid #6a1b9a; width: 150px;"></div>
        </div>
        <div>
            <p style="font-weight: bold;">التاريخ: {{ now.strftime('%Y/%m/%d') }}</p>
            <p>اسم المستخدم: {{ current_user.full_name or current_user.username }}</p>
        </div>
    </div>
    
    <!-- رقم الصفحة -->
    <div class="page-number">
        صفحة {{ page_number|default('1') }}
    </div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // طباعة التقرير
        document.querySelector('.btn-print').addEventListener('click', function() {
            window.print();
        });

        // رجوع للصفحة السابقة
        document.querySelector('.btn-back').addEventListener('click', function() {
            window.history.back();
        });
    });
</script>
{% endblock %}
