"""
Enhanced validation utilities for HRM System
"""
import re
from flask import request, jsonify
from functools import wraps
import bleach


def validate_military_id(military_id):
    """Validate military ID format."""
    if not military_id:
        return False, "الرقم العسكري مطلوب"
    
    # Remove spaces and check if it's numeric
    clean_id = military_id.replace(' ', '')
    if not clean_id.isdigit():
        return False, "الرقم العسكري يجب أن يحتوي على أرقام فقط"
    
    # Check length (assuming 5-10 digits)
    if len(clean_id) < 3 or len(clean_id) > 10:
        return False, "الرقم العسكري يجب أن يكون بين 3 و 10 أرقام"
    
    return True, ""


def validate_national_id(national_id):
    """Validate Saudi national ID format."""
    if not national_id:
        return True, ""  # Optional field
    
    # Remove spaces
    clean_id = national_id.replace(' ', '')
    
    # Check if it's exactly 10 digits
    if not clean_id.isdigit() or len(clean_id) != 10:
        return False, "رقم الهوية يجب أن يكون 10 أرقام"
    
    # Check if it starts with 1 or 2 (Saudi nationals)
    if not clean_id.startswith(('1', '2')):
        return False, "رقم الهوية يجب أن يبدأ بـ 1 أو 2"
    
    return True, ""


def validate_email(email):
    """Validate email format."""
    if not email:
        return True, ""  # Optional field
    
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, email):
        return False, "صيغة البريد الإلكتروني غير صحيحة"
    
    return True, ""


def validate_phone(phone):
    """Validate phone number format."""
    if not phone:
        return True, ""  # Optional field
    
    # Remove spaces, dashes, and parentheses
    clean_phone = re.sub(r'[\s\-\(\)]', '', phone)
    
    # Check Saudi phone number format
    saudi_pattern = r'^((\+966)|0)?5[0-9]{8}$'
    if not re.match(saudi_pattern, clean_phone):
        return False, "رقم الهاتف يجب أن يكون رقم سعودي صحيح (05xxxxxxxx)"
    
    return True, ""


def validate_password(password):
    """Validate password strength."""
    if not password:
        return False, "كلمة المرور مطلوبة"
    
    if len(password) < 8:
        return False, "كلمة المرور يجب أن تكون 8 أحرف على الأقل"
    
    # Check for at least one digit
    if not re.search(r'\d', password):
        return False, "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل"
    
    # Check for at least one letter
    if not re.search(r'[a-zA-Z]', password):
        return False, "كلمة المرور يجب أن تحتوي على حرف واحد على الأقل"
    
    return True, ""


def sanitize_input(text):
    """Sanitize user input to prevent XSS."""
    if not text:
        return text
    
    # Allow basic HTML tags for rich text fields
    allowed_tags = ['b', 'i', 'u', 'strong', 'em', 'p', 'br']
    allowed_attributes = {}
    
    return bleach.clean(text, tags=allowed_tags, attributes=allowed_attributes, strip=True)


def validate_date_range(start_date, end_date):
    """Validate date range."""
    if not start_date or not end_date:
        return False, "تاريخ البداية والنهاية مطلوبان"
    
    if start_date >= end_date:
        return False, "تاريخ النهاية يجب أن يكون بعد تاريخ البداية"
    
    return True, ""


def validate_json_input(required_fields=None):
    """Decorator to validate JSON input."""
    def decorator(f):
        @wraps(f)
        def decorated(*args, **kwargs):
            if not request.is_json:
                return jsonify({'error': 'Content-Type must be application/json'}), 400
            
            data = request.get_json()
            if not data:
                return jsonify({'error': 'No JSON data provided'}), 400
            
            # Check required fields
            if required_fields:
                missing_fields = []
                for field in required_fields:
                    if field not in data or not data[field]:
                        missing_fields.append(field)
                
                if missing_fields:
                    return jsonify({
                        'error': 'Missing required fields',
                        'missing_fields': missing_fields
                    }), 400
            
            # Sanitize string inputs
            for key, value in data.items():
                if isinstance(value, str):
                    data[key] = sanitize_input(value)
            
            return f(*args, **kwargs)
        return decorated
    return decorator


def validate_employee_data(data):
    """Validate employee data comprehensively."""
    errors = {}
    
    # Validate military ID
    valid, msg = validate_military_id(data.get('military_id'))
    if not valid:
        errors['military_id'] = msg
    
    # Validate name
    if not data.get('name') or len(data.get('name', '').strip()) < 2:
        errors['name'] = "الاسم مطلوب ويجب أن يكون حرفين على الأقل"
    
    # Validate national ID
    valid, msg = validate_national_id(data.get('national_id'))
    if not valid:
        errors['national_id'] = msg
    
    # Validate email
    valid, msg = validate_email(data.get('email'))
    if not valid:
        errors['email'] = msg
    
    # Validate phone
    valid, msg = validate_phone(data.get('phone'))
    if not valid:
        errors['phone'] = msg
    
    # Validate blood type
    blood_types = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-']
    if data.get('blood_type') and data['blood_type'] not in blood_types:
        errors['blood_type'] = "فصيلة الدم غير صحيحة"
    
    return errors


def validate_leave_data(data):
    """Validate leave request data."""
    errors = {}
    
    # Validate employee ID
    if not data.get('employee_id'):
        errors['employee_id'] = "معرف الموظف مطلوب"
    
    # Validate dates
    if not data.get('start_date'):
        errors['start_date'] = "تاريخ البداية مطلوب"
    
    if not data.get('end_date'):
        errors['end_date'] = "تاريخ النهاية مطلوب"
    
    # Validate reason
    if not data.get('reason') or len(data.get('reason', '').strip()) < 5:
        errors['reason'] = "سبب الإجازة مطلوب ويجب أن يكون 5 أحرف على الأقل"
    
    return errors
