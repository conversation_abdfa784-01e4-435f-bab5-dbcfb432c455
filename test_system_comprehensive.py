#!/usr/bin/env python3
"""
اختبار شامل لنظام إدارة الموارد البشرية
"""
import os
import sys
import requests
import time
import json
from datetime import datetime
import subprocess

class HRMSystemTester:
    def __init__(self):
        self.base_url = "http://127.0.0.1:5000"
        self.test_results = []
        self.errors = []
        self.session = requests.Session()
        
    def test_server_availability(self):
        """اختبار توفر الخادم"""
        print("🔍 اختبار توفر الخادم...")
        
        try:
            response = self.session.get(self.base_url, timeout=10)
            if response.status_code == 200:
                self.test_results.append({
                    'test': 'server_availability',
                    'status': 'pass',
                    'message': 'الخادم متاح ويعمل بشكل طبيعي'
                })
                print("    ✅ الخادم متاح ويعمل")
                return True
            else:
                self.test_results.append({
                    'test': 'server_availability',
                    'status': 'fail',
                    'message': f'الخادم يرد بكود خطأ: {response.status_code}'
                })
                print(f"    ❌ الخادم يرد بكود خطأ: {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            self.test_results.append({
                'test': 'server_availability',
                'status': 'fail',
                'message': 'لا يمكن الاتصال بالخادم'
            })
            print("    ❌ لا يمكن الاتصال بالخادم")
            return False
        except Exception as e:
            self.test_results.append({
                'test': 'server_availability',
                'status': 'error',
                'message': f'خطأ في الاختبار: {str(e)}'
            })
            print(f"    ❌ خطأ في الاختبار: {e}")
            return False
    
    def test_login_functionality(self):
        """اختبار وظيفة تسجيل الدخول"""
        print("\n🔐 اختبار وظيفة تسجيل الدخول...")
        
        try:
            # الحصول على صفحة تسجيل الدخول
            login_page = self.session.get(f"{self.base_url}/auth/login")
            if login_page.status_code != 200:
                self.test_results.append({
                    'test': 'login_page_access',
                    'status': 'fail',
                    'message': f'لا يمكن الوصول لصفحة تسجيل الدخول: {login_page.status_code}'
                })
                print(f"    ❌ لا يمكن الوصول لصفحة تسجيل الدخول: {login_page.status_code}")
                return False
            
            print("    ✅ يمكن الوصول لصفحة تسجيل الدخول")
            
            # محاولة تسجيل دخول بمعلومات خاطئة
            login_data = {
                'username': 'wrong_user',
                'password': 'wrong_password'
            }
            
            wrong_login = self.session.post(f"{self.base_url}/auth/login", data=login_data)
            if 'خطأ' in wrong_login.text or 'error' in wrong_login.text.lower():
                print("    ✅ النظام يرفض المعلومات الخاطئة بشكل صحيح")
                self.test_results.append({
                    'test': 'login_security',
                    'status': 'pass',
                    'message': 'النظام يرفض المعلومات الخاطئة'
                })
            else:
                print("    ⚠️ قد يكون هناك مشكلة في أمان تسجيل الدخول")
                self.test_results.append({
                    'test': 'login_security',
                    'status': 'warning',
                    'message': 'قد يكون هناك مشكلة في أمان تسجيل الدخول'
                })
            
            # محاولة تسجيل دخول بالمعلومات الصحيحة
            correct_login_data = {
                'username': 'admin',
                'password': 'admin123'
            }
            
            correct_login = self.session.post(f"{self.base_url}/auth/login", data=correct_login_data)
            if correct_login.status_code == 200 and 'dashboard' in correct_login.url:
                print("    ✅ تسجيل الدخول بالمعلومات الصحيحة يعمل")
                self.test_results.append({
                    'test': 'login_functionality',
                    'status': 'pass',
                    'message': 'تسجيل الدخول يعمل بشكل صحيح'
                })
                return True
            else:
                print("    ❌ مشكلة في تسجيل الدخول بالمعلومات الصحيحة")
                self.test_results.append({
                    'test': 'login_functionality',
                    'status': 'fail',
                    'message': 'مشكلة في تسجيل الدخول'
                })
                return False
                
        except Exception as e:
            self.test_results.append({
                'test': 'login_functionality',
                'status': 'error',
                'message': f'خطأ في اختبار تسجيل الدخول: {str(e)}'
            })
            print(f"    ❌ خطأ في اختبار تسجيل الدخول: {e}")
            return False
    
    def test_main_pages(self):
        """اختبار الصفحات الرئيسية"""
        print("\n📄 اختبار الصفحات الرئيسية...")
        
        main_pages = [
            ('/dashboard/', 'لوحة التحكم'),
            ('/employees/', 'إدارة الموظفين'),
            ('/leaves/', 'إدارة الإجازات'),
            ('/reports/', 'التقارير'),
            ('/users/', 'إدارة المستخدمين')
        ]
        
        for url, name in main_pages:
            try:
                response = self.session.get(f"{self.base_url}{url}")
                if response.status_code == 200:
                    print(f"    ✅ {name}: متاح")
                    self.test_results.append({
                        'test': f'page_access_{url}',
                        'status': 'pass',
                        'message': f'{name} متاح'
                    })
                else:
                    print(f"    ❌ {name}: غير متاح ({response.status_code})")
                    self.test_results.append({
                        'test': f'page_access_{url}',
                        'status': 'fail',
                        'message': f'{name} غير متاح - كود الخطأ: {response.status_code}'
                    })
                    
            except Exception as e:
                print(f"    ❌ {name}: خطأ في الوصول - {e}")
                self.test_results.append({
                    'test': f'page_access_{url}',
                    'status': 'error',
                    'message': f'خطأ في الوصول لـ {name}: {str(e)}'
                })
    
    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        print("\n🗄️ اختبار الاتصال بقاعدة البيانات...")
        
        try:
            # محاولة الوصول لصفحة تحتاج لقاعدة البيانات
            response = self.session.get(f"{self.base_url}/employees/")
            if response.status_code == 200:
                print("    ✅ قاعدة البيانات متصلة وتعمل")
                self.test_results.append({
                    'test': 'database_connection',
                    'status': 'pass',
                    'message': 'قاعدة البيانات متصلة وتعمل'
                })
                return True
            else:
                print(f"    ❌ مشكلة في قاعدة البيانات: {response.status_code}")
                self.test_results.append({
                    'test': 'database_connection',
                    'status': 'fail',
                    'message': f'مشكلة في قاعدة البيانات: {response.status_code}'
                })
                return False
                
        except Exception as e:
            print(f"    ❌ خطأ في اختبار قاعدة البيانات: {e}")
            self.test_results.append({
                'test': 'database_connection',
                'status': 'error',
                'message': f'خطأ في اختبار قاعدة البيانات: {str(e)}'
            })
            return False
    
    def test_static_files(self):
        """اختبار الملفات الثابتة (CSS, JS, Images)"""
        print("\n🎨 اختبار الملفات الثابتة...")
        
        static_files = [
            '/static/css/main.css',
            '/static/css/print.css',
            '/static/js/main.js',
            '/static/img/logo.png'
        ]
        
        for file_url in static_files:
            try:
                response = self.session.get(f"{self.base_url}{file_url}")
                if response.status_code == 200:
                    print(f"    ✅ {file_url}: متاح")
                    self.test_results.append({
                        'test': f'static_file_{file_url}',
                        'status': 'pass',
                        'message': f'{file_url} متاح'
                    })
                else:
                    print(f"    ❌ {file_url}: غير متاح ({response.status_code})")
                    self.test_results.append({
                        'test': f'static_file_{file_url}',
                        'status': 'fail',
                        'message': f'{file_url} غير متاح'
                    })
                    
            except Exception as e:
                print(f"    ❌ {file_url}: خطأ - {e}")
                self.test_results.append({
                    'test': f'static_file_{file_url}',
                    'status': 'error',
                    'message': f'خطأ في {file_url}: {str(e)}'
                })
    
    def test_arabic_encoding(self):
        """اختبار دعم الترميز العربي"""
        print("\n🔤 اختبار دعم الترميز العربي...")
        
        try:
            response = self.session.get(f"{self.base_url}/dashboard/")
            if response.status_code == 200:
                content = response.text
                
                # فحص وجود النصوص العربية
                arabic_texts = ['الموظفين', 'الإجازات', 'التقارير', 'لوحة التحكم']
                arabic_found = any(text in content for text in arabic_texts)
                
                if arabic_found:
                    print("    ✅ النصوص العربية تظهر بشكل صحيح")
                    self.test_results.append({
                        'test': 'arabic_encoding',
                        'status': 'pass',
                        'message': 'النصوص العربية تظهر بشكل صحيح'
                    })
                else:
                    print("    ⚠️ قد تكون هناك مشكلة في عرض النصوص العربية")
                    self.test_results.append({
                        'test': 'arabic_encoding',
                        'status': 'warning',
                        'message': 'قد تكون هناك مشكلة في عرض النصوص العربية'
                    })
                
                # فحص وجود meta charset
                if 'charset=UTF-8' in content or 'charset="UTF-8"' in content:
                    print("    ✅ ترميز UTF-8 محدد بشكل صحيح")
                    self.test_results.append({
                        'test': 'utf8_charset',
                        'status': 'pass',
                        'message': 'ترميز UTF-8 محدد بشكل صحيح'
                    })
                else:
                    print("    ⚠️ ترميز UTF-8 قد يكون غير محدد")
                    self.test_results.append({
                        'test': 'utf8_charset',
                        'status': 'warning',
                        'message': 'ترميز UTF-8 قد يكون غير محدد'
                    })
                    
        except Exception as e:
            print(f"    ❌ خطأ في اختبار الترميز العربي: {e}")
            self.test_results.append({
                'test': 'arabic_encoding',
                'status': 'error',
                'message': f'خطأ في اختبار الترميز العربي: {str(e)}'
            })
    
    def test_print_functionality(self):
        """اختبار وظيفة الطباعة"""
        print("\n🖨️ اختبار وظيفة الطباعة...")
        
        print_pages = [
            '/reports/employees_print',
            '/employees/1/print',  # قد لا يعمل إذا لم يكن هناك موظف برقم 1
        ]
        
        for page_url in print_pages:
            try:
                response = self.session.get(f"{self.base_url}{page_url}")
                if response.status_code == 200:
                    content = response.text
                    
                    # فحص وجود CSS الطباعة
                    if 'print.css' in content:
                        print(f"    ✅ {page_url}: CSS الطباعة موجود")
                        self.test_results.append({
                            'test': f'print_css_{page_url}',
                            'status': 'pass',
                            'message': f'{page_url} يحتوي على CSS الطباعة'
                        })
                    else:
                        print(f"    ⚠️ {page_url}: CSS الطباعة قد يكون مفقود")
                        self.test_results.append({
                            'test': f'print_css_{page_url}',
                            'status': 'warning',
                            'message': f'{page_url} قد لا يحتوي على CSS الطباعة'
                        })
                        
                elif response.status_code == 404:
                    print(f"    ℹ️ {page_url}: الصفحة غير موجودة (طبيعي إذا لم تكن مُنشأة)")
                    self.test_results.append({
                        'test': f'print_page_{page_url}',
                        'status': 'info',
                        'message': f'{page_url} غير موجودة'
                    })
                else:
                    print(f"    ❌ {page_url}: خطأ ({response.status_code})")
                    self.test_results.append({
                        'test': f'print_page_{page_url}',
                        'status': 'fail',
                        'message': f'{page_url} خطأ: {response.status_code}'
                    })
                    
            except Exception as e:
                print(f"    ❌ {page_url}: خطأ - {e}")
                self.test_results.append({
                    'test': f'print_page_{page_url}',
                    'status': 'error',
                    'message': f'خطأ في {page_url}: {str(e)}'
                })
    
    def generate_test_report(self):
        """إنشاء تقرير الاختبارات"""
        print("\n📊 إنشاء تقرير الاختبارات...")
        
        # حساب الإحصائيات
        total_tests = len(self.test_results)
        passed_tests = len([t for t in self.test_results if t['status'] == 'pass'])
        failed_tests = len([t for t in self.test_results if t['status'] == 'fail'])
        warning_tests = len([t for t in self.test_results if t['status'] == 'warning'])
        error_tests = len([t for t in self.test_results if t['status'] == 'error'])
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_tests': total_tests,
                'passed': passed_tests,
                'failed': failed_tests,
                'warnings': warning_tests,
                'errors': error_tests,
                'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0
            },
            'test_results': self.test_results,
            'errors': self.errors
        }
        
        # حفظ التقرير
        report_file = f"system_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # إنشاء تقرير نصي
        text_report = self._generate_text_test_report(report)
        text_report_file = f"system_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(text_report_file, 'w', encoding='utf-8') as f:
            f.write(text_report)
        
        print(f"✅ تم إنشاء تقرير الاختبارات: {report_file}")
        print(f"✅ تم إنشاء التقرير النصي: {text_report_file}")
        
        return report_file, text_report_file, report
    
    def _generate_text_test_report(self, report):
        """إنشاء تقرير نصي للاختبارات"""
        text = f"""
🧪 تقرير الاختبار الشامل لنظام إدارة الموارد البشرية
═══════════════════════════════════════════════════════════

📅 التاريخ والوقت: {report['timestamp']}

📊 ملخص النتائج:
═══════════════════════════════════════════════════════════
• إجمالي الاختبارات: {report['summary']['total_tests']}
• الاختبارات الناجحة: {report['summary']['passed']} ✅
• الاختبارات الفاشلة: {report['summary']['failed']} ❌
• التحذيرات: {report['summary']['warnings']} ⚠️
• الأخطاء: {report['summary']['errors']} 🔥
• معدل النجاح: {report['summary']['success_rate']:.1f}%

"""
        
        # تجميع النتائج حسب الحالة
        for status, emoji in [('pass', '✅'), ('fail', '❌'), ('warning', '⚠️'), ('error', '🔥')]:
            status_tests = [t for t in report['test_results'] if t['status'] == status]
            if status_tests:
                status_names = {
                    'pass': 'الاختبارات الناجحة',
                    'fail': 'الاختبارات الفاشلة', 
                    'warning': 'التحذيرات',
                    'error': 'الأخطاء'
                }
                text += f"\n{emoji} {status_names[status]}:\n"
                text += "═" * 50 + "\n"
                for i, test in enumerate(status_tests, 1):
                    text += f"{i}. {test['test']}: {test['message']}\n"
        
        text += "\n🎯 التوصيات:\n"
        text += "═" * 50 + "\n"
        
        if report['summary']['failed'] > 0:
            text += "• إصلاح الاختبارات الفاشلة قبل النشر\n"
        if report['summary']['warnings'] > 0:
            text += "• مراجعة التحذيرات وإصلاحها إن أمكن\n"
        if report['summary']['success_rate'] < 80:
            text += "• معدل النجاح منخفض - يحتاج مراجعة شاملة\n"
        if report['summary']['success_rate'] >= 90:
            text += "• النظام يعمل بشكل ممتاز! 🎉\n"
        
        text += "• تشغيل الاختبارات بشكل دوري\n"
        text += "• مراقبة أداء النظام\n"
        text += "• إنشاء نسخ احتياطية دورية\n"
        
        return text

def main():
    """الدالة الرئيسية"""
    print("🧪 أداة الاختبار الشامل لنظام إدارة الموارد البشرية")
    print("=" * 70)
    
    tester = HRMSystemTester()
    
    # تشغيل الاختبارات
    server_available = tester.test_server_availability()
    
    if server_available:
        tester.test_login_functionality()
        tester.test_main_pages()
        tester.test_database_connection()
        tester.test_static_files()
        tester.test_arabic_encoding()
        tester.test_print_functionality()
    else:
        print("\n❌ الخادم غير متاح - لا يمكن تشغيل باقي الاختبارات")
        print("تأكد من تشغيل الخادم أولاً باستخدام: python hrmsystem/run.py")
    
    # إنشاء التقرير
    report_file, text_report_file, report = tester.generate_test_report()
    
    # عرض النتائج النهائية
    print("\n" + "=" * 70)
    print("🎉 تم الانتهاء من الاختبارات!")
    print("=" * 70)
    print(f"📊 النتائج:")
    print(f"   • إجمالي الاختبارات: {report['summary']['total_tests']}")
    print(f"   • الناجحة: {report['summary']['passed']} ✅")
    print(f"   • الفاشلة: {report['summary']['failed']} ❌")
    print(f"   • التحذيرات: {report['summary']['warnings']} ⚠️")
    print(f"   • معدل النجاح: {report['summary']['success_rate']:.1f}%")
    print(f"   • التقرير: {text_report_file}")

if __name__ == '__main__':
    main()
