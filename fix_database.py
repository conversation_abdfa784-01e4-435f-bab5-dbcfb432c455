#!/usr/bin/env python3
"""
Database Fix Script for HRM System
This script will reset the database and create fresh tables with sample data.
"""
import os
import sys

def fix_database():
    """Fix the database completely."""
    print("🗄️  Fixing HRM Database...")
    
    # Remove existing database files
    db_files = [
        'hrmsystem/hrm.db',
        'hrmsystem/hrm_enhanced.db',
        'hrm.db',
        'hrm_enhanced.db',
        'instance/hrm.db',
        'app.db'
    ]
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                os.remove(db_file)
                print(f"  🗑️  Removed {db_file}")
            except Exception as e:
                print(f"  ⚠️  Could not remove {db_file}: {e}")
    
    print("✅ Database files cleaned")
    
    # Now create fresh database
    try:
        from hrmsystem.application import create_app, db
        from hrmsystem.application.models import User, UserRole, Department, Employee, LeaveType
        
        app = create_app()
        with app.app_context():
            # Create all tables
            db.create_all()
            print("✅ Database tables created")
            
            # Create default departments
            default_departments = [
                {'name': 'القيادة العامة', 'description': 'وحدة القيادة العامة للقوات المسلحة'},
                {'name': 'الكتيبة الأولى', 'description': 'وحدة الكتيبة الأولى'},
                {'name': 'الكتيبة الثانية', 'description': 'وحدة الكتيبة الثانية'},
                {'name': 'الكتيبة الثالثة', 'description': 'وحدة الكتيبة الثالثة'},
                {'name': 'الإدارة العامة', 'description': 'وحدة الإدارة العامة'}
            ]
            
            for dept_data in default_departments:
                dept = Department(name=dept_data['name'], description=dept_data['description'])
                db.session.add(dept)
            
            db.session.commit()
            print("✅ Default departments created")
            
            # Create default leave types
            default_leave_types = [
                {'name': 'إجازة اعتيادية', 'description': 'الإجازة الاعتيادية السنوية', 'color': '#007bff'},
                {'name': 'إجازة مرضية', 'description': 'إجازة للعلاج والراحة الطبية', 'color': '#dc3545'},
                {'name': 'إجازة طارئة', 'description': 'إجازة للظروف الطارئة', 'color': '#ffc107'},
                {'name': 'إجازة أمومة', 'description': 'إجازة الأمومة والولادة', 'color': '#e83e8c'},
                {'name': 'إجازة حج', 'description': 'إجازة لأداء فريضة الحج', 'color': '#28a745'}
            ]
            
            for leave_type_data in default_leave_types:
                leave_type = LeaveType(
                    name=leave_type_data['name'],
                    description=leave_type_data['description'],
                    color=leave_type_data['color']
                )
                db.session.add(leave_type)
            
            db.session.commit()
            print("✅ Default leave types created")
            
            # Create admin user
            admin = User(
                username='admin',
                email='<EMAIL>',
                role=UserRole.ADMIN,
                is_admin=True,
                full_name='مدير النظام'
            )
            admin.set_password('admin123')
            db.session.add(admin)
            
            # Create supervisor user
            supervisor = User(
                username='supervisor',
                email='<EMAIL>',
                role=UserRole.SUPERVISOR,
                full_name='مشرف النظام'
            )
            supervisor.set_password('supervisor123')
            db.session.add(supervisor)
            
            # Create regular user
            user = User(
                username='user',
                email='<EMAIL>',
                role=UserRole.USER,
                full_name='مستخدم عادي'
            )
            user.set_password('user123')
            db.session.add(user)
            
            db.session.commit()
            print("✅ Default users created")
            
            print("\n🎉 Database fix completed successfully!")
            print("\n📋 Default Login Credentials:")
            print("   👑 Admin: admin / admin123")
            print("   👮 Supervisor: supervisor / supervisor123")
            print("   👤 User: user / user123")
            
            return True
            
    except Exception as e:
        print(f"❌ Database fix failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    print("🔄 HRM Database Fix Tool")
    print("=" * 50)
    
    if fix_database():
        print("\n✅ Database fix successful!")
        print("You can now run: python start_enhanced_hrm.py")
    else:
        print("\n❌ Database fix failed!")
        sys.exit(1)
