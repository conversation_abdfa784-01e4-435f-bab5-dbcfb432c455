"""
Leave management tests for HRM System
"""
import pytest
from datetime import date, timedelta
from hrmsystem.application.models import LeaveRequest, LeaveStatus, LeaveType


class TestLeaveViews:
    """Test leave view functionality."""
    
    def test_leaves_list_requires_login(self, client):
        """Test that leaves list requires authentication."""
        response = client.get('/leaves/')
        assert response.status_code == 302
        assert '/auth/login' in response.location
    
    def test_leaves_list_loads(self, authenticated_client):
        """Test that leaves list loads for authenticated user."""
        response = authenticated_client.get('/leaves/')
        assert response.status_code == 200
        assert 'طلبات الإجازات' in response.get_data(as_text=True)
    
    def test_leave_create_form_loads(self, authenticated_client, sample_employee):
        """Test that leave creation form loads."""
        response = authenticated_client.get('/leaves/create')
        assert response.status_code == 200
        assert 'تقديم طلب إجازة' in response.get_data(as_text=True)


class TestLeaveCreation:
    """Test leave request creation functionality."""
    
    def test_create_leave_valid_data(self, authenticated_client, sample_employee, app):
        """Test creating leave request with valid data."""
        start_date = date.today() + timedelta(days=7)
        end_date = start_date + timedelta(days=5)
        
        response = authenticated_client.post('/leaves/create', data={
            'employee_id': sample_employee.id,
            'leave_type_id': 1,  # Assuming leave type exists
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'reason': 'إجازة اعتيادية'
        }, follow_redirects=True)
        
        assert response.status_code == 200
        assert 'تم تقديم طلب الإجازة بنجاح' in response.get_data(as_text=True)
        
        # Verify leave request was created in database
        with app.app_context():
            leave_request = LeaveRequest.query.filter_by(employee_id=sample_employee.id).first()
            assert leave_request is not None
            assert leave_request.status == LeaveStatus.PENDING
    
    def test_create_leave_invalid_dates(self, authenticated_client, sample_employee):
        """Test creating leave request with invalid dates."""
        start_date = date.today() + timedelta(days=7)
        end_date = start_date - timedelta(days=2)  # End before start
        
        response = authenticated_client.post('/leaves/create', data={
            'employee_id': sample_employee.id,
            'leave_type_id': 1,
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'reason': 'إجازة خاطئة'
        })
        
        assert response.status_code == 200
        assert 'تاريخ الانتهاء يجب أن يكون بعد تاريخ البداية' in response.get_data(as_text=True)
    
    def test_create_leave_past_dates(self, authenticated_client, sample_employee):
        """Test creating leave request with past dates."""
        start_date = date.today() - timedelta(days=7)
        end_date = date.today() - timedelta(days=2)
        
        response = authenticated_client.post('/leaves/create', data={
            'employee_id': sample_employee.id,
            'leave_type_id': 1,
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'reason': 'إجازة في الماضي'
        })
        
        assert response.status_code == 200
        assert 'لا يمكن تقديم طلب إجازة في الماضي' in response.get_data(as_text=True)


class TestLeaveApproval:
    """Test leave approval functionality."""
    
    def test_approve_leave_request(self, authenticated_client, sample_employee, admin_user, app):
        """Test approving a leave request."""
        # First create a leave request
        with app.app_context():
            from hrmsystem.application import db
            
            leave_request = LeaveRequest(
                employee_id=sample_employee.id,
                leave_type_id=1,
                start_date=date.today() + timedelta(days=7),
                end_date=date.today() + timedelta(days=12),
                total_days=5,
                reason='إجازة للاختبار',
                status=LeaveStatus.PENDING
            )
            db.session.add(leave_request)
            db.session.commit()
            leave_id = leave_request.id
        
        # Approve the leave request
        response = authenticated_client.post(f'/leaves/approve/{leave_id}', 
                                           follow_redirects=True)
        
        assert response.status_code == 200
        assert 'تم الموافقة على طلب الإجازة' in response.get_data(as_text=True)
        
        # Verify leave request was approved in database
        with app.app_context():
            approved_leave = LeaveRequest.query.get(leave_id)
            assert approved_leave.status == LeaveStatus.APPROVED
            assert approved_leave.approved_by == admin_user.id
    
    def test_reject_leave_request(self, authenticated_client, sample_employee, app):
        """Test rejecting a leave request."""
        # First create a leave request
        with app.app_context():
            from hrmsystem.application import db
            
            leave_request = LeaveRequest(
                employee_id=sample_employee.id,
                leave_type_id=1,
                start_date=date.today() + timedelta(days=7),
                end_date=date.today() + timedelta(days=12),
                total_days=5,
                reason='إجازة للاختبار',
                status=LeaveStatus.PENDING
            )
            db.session.add(leave_request)
            db.session.commit()
            leave_id = leave_request.id
        
        # Reject the leave request
        response = authenticated_client.post(f'/leaves/reject/{leave_id}', data={
            'rejection_reason': 'عدم توفر بديل'
        }, follow_redirects=True)
        
        assert response.status_code == 200
        assert 'تم رفض طلب الإجازة' in response.get_data(as_text=True)
        
        # Verify leave request was rejected in database
        with app.app_context():
            rejected_leave = LeaveRequest.query.get(leave_id)
            assert rejected_leave.status == LeaveStatus.REJECTED
            assert rejected_leave.rejection_reason == 'عدم توفر بديل'


class TestLeaveTypes:
    """Test leave types functionality."""
    
    def test_leave_types_list(self, authenticated_client):
        """Test that leave types list loads."""
        response = authenticated_client.get('/leaves/types')
        assert response.status_code == 200
        assert 'أنواع الإجازات' in response.get_data(as_text=True)
    
    def test_create_leave_type(self, authenticated_client, app):
        """Test creating a new leave type."""
        response = authenticated_client.post('/leaves/types/create', data={
            'name': 'إجازة طارئة',
            'description': 'إجازة للحالات الطارئة',
            'color': '#ff0000'
        }, follow_redirects=True)
        
        assert response.status_code == 200
        assert 'تم إضافة نوع الإجازة بنجاح' in response.get_data(as_text=True)
        
        # Verify leave type was created in database
        with app.app_context():
            leave_type = LeaveType.query.filter_by(name='إجازة طارئة').first()
            assert leave_type is not None
            assert leave_type.description == 'إجازة للحالات الطارئة'


class TestLeaveCalendar:
    """Test leave calendar functionality."""
    
    def test_leave_calendar_loads(self, authenticated_client):
        """Test that leave calendar loads."""
        response = authenticated_client.get('/leaves/calendar')
        assert response.status_code == 200
        assert 'تقويم الإجازات' in response.get_data(as_text=True)
    
    def test_leave_calendar_api(self, authenticated_client, sample_employee, app):
        """Test leave calendar API endpoint."""
        # Create a leave request for testing
        with app.app_context():
            from hrmsystem.application import db
            
            leave_request = LeaveRequest(
                employee_id=sample_employee.id,
                leave_type_id=1,
                start_date=date.today() + timedelta(days=7),
                end_date=date.today() + timedelta(days=12),
                total_days=5,
                reason='إجازة للاختبار',
                status=LeaveStatus.APPROVED
            )
            db.session.add(leave_request)
            db.session.commit()
        
        # Test API endpoint
        response = authenticated_client.get('/leaves/api/calendar')
        assert response.status_code == 200
        
        # Should return JSON data
        data = response.get_json()
        assert isinstance(data, list)


class TestLeaveReports:
    """Test leave reports functionality."""
    
    def test_leave_report_loads(self, authenticated_client):
        """Test that leave report loads."""
        response = authenticated_client.get('/leaves/report')
        assert response.status_code == 200
        assert 'تقرير الإجازات' in response.get_data(as_text=True)
    
    def test_leave_report_with_filters(self, authenticated_client):
        """Test leave report with date filters."""
        start_date = date.today() - timedelta(days=30)
        end_date = date.today()
        
        response = authenticated_client.get('/leaves/report', query_string={
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d')
        })
        
        assert response.status_code == 200
        assert 'تقرير الإجازات' in response.get_data(as_text=True)
