#!/usr/bin/env python3
"""
Enhanced HRM System Startup Script
This script starts the HRM system with all security enhancements and monitoring.
"""
import os
import sys
import subprocess
import argparse
from datetime import datetime


def print_banner():
    """Print startup banner."""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🏢 نظام إدارة الموارد البشرية المحسن                  ║
    ║              Enhanced HRM System v2.0                       ║
    ║                                                              ║
    ║  ✅ Error Handlers    ✅ API Endpoints                       ║
    ║  ✅ Rate Limiting     ✅ Security Validation                 ║
    ║  ✅ Comprehensive Tests ✅ Advanced Logging                  ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_dependencies():
    """Check if all required dependencies are installed."""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'flask', 'flask-sqlalchemy', 'flask-login', 'flask-wtf',
        'flask-migrate', 'flask-bcrypt', 'pillow', 'pyjwt'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("Installing missing packages...")
        
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ Installed {package}")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {package}")
                return False
    
    print("✅ All dependencies are installed")
    return True


def setup_environment():
    """Set up environment variables."""
    print("🔧 Setting up environment...")
    
    # Set default environment variables if not already set
    env_vars = {
        'FLASK_ENV': 'development',
        'SECRET_KEY': 'enhanced-hrm-secret-key-2024',
        'DATABASE_URL': 'sqlite:///hrm_enhanced.db'
    }
    
    for key, value in env_vars.items():
        if key not in os.environ:
            os.environ[key] = value
            print(f"  📝 Set {key}")
    
    print("✅ Environment configured")


def create_directories():
    """Create necessary directories."""
    print("📁 Creating directories...")
    
    directories = [
        'logs',
        'hrmsystem/application/static/uploads',
        'instance',
        'htmlcov'  # For test coverage reports
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"  📂 Created {directory}")
    
    print("✅ Directories created")


def initialize_database():
    """Initialize the database."""
    print("🗄️  Initializing database...")
    
    try:
        # Import and initialize (no need to change directory)
        from hrmsystem.application import create_app, db

        app = create_app()
        with app.app_context():
            db.create_all()
            print("✅ Database initialized")

            # Create default admin user if not exists
            from hrmsystem.application.models import User, UserRole, Department

            # Create default departments first
            default_departments = [
                {'name': 'القيادة العامة', 'description': 'وحدة القيادة العامة للقوات المسلحة'},
                {'name': 'الكتيبة الأولى', 'description': 'وحدة الكتيبة الأولى'},
                {'name': 'الكتيبة الثانية', 'description': 'وحدة الكتيبة الثانية'},
                {'name': 'الإدارة العامة', 'description': 'وحدة الإدارة العامة'}
            ]

            for dept_data in default_departments:
                existing_dept = Department.query.filter_by(name=dept_data['name']).first()
                if not existing_dept:
                    dept = Department(name=dept_data['name'], description=dept_data['description'])
                    db.session.add(dept)

            db.session.commit()
            print("✅ Default departments created")

            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    role=UserRole.ADMIN,
                    is_admin=True,
                    full_name='مدير النظام'
                )
                admin.set_password('admin123')
                db.session.add(admin)
                db.session.commit()
                print("✅ Default admin user created")
            else:
                print("ℹ️  Admin user already exists")
        

    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False
    
    return True


def run_security_check():
    """Run basic security checks."""
    print("🛡️  Running security checks...")
    
    checks = [
        ("Secret key", lambda: os.environ.get('SECRET_KEY') != 'your_secret_key'),
        ("Debug mode", lambda: os.environ.get('FLASK_ENV') != 'production' or os.environ.get('FLASK_DEBUG') != 'True'),
        ("Database URL", lambda: 'sqlite:///' in os.environ.get('DATABASE_URL', '')),
    ]
    
    for check_name, check_func in checks:
        try:
            if check_func():
                print(f"  ✅ {check_name}")
            else:
                print(f"  ⚠️  {check_name} - needs attention")
        except Exception:
            print(f"  ❓ {check_name} - could not verify")
    
    print("✅ Security check completed")


def start_application(host='127.0.0.1', port=5000, debug=True):
    """Start the Flask application."""
    print(f"🚀 Starting HRM System on http://{host}:{port}")
    print(f"📅 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n" + "="*60)
    print("🔗 Available URLs:")
    print(f"   • Main Application: http://{host}:{port}")
    print(f"   • API Documentation: http://{host}:{port}/api")
    print(f"   • Admin Panel: http://{host}:{port}/users")
    print("="*60)
    print("\n📋 Default Login Credentials:")
    print("   • Username: admin")
    print("   • Password: admin123")
    print("="*60)
    print("\n🛠️  Available Features:")
    print("   ✅ Employee Management")
    print("   ✅ Leave Management") 
    print("   ✅ User Management")
    print("   ✅ Reports & Analytics")
    print("   ✅ API Endpoints")
    print("   ✅ Security Features")
    print("   ✅ Error Handling")
    print("   ✅ Rate Limiting")
    print("="*60)
    print("\n⚡ Press Ctrl+C to stop the server")
    print("="*60 + "\n")
    
    try:
        # Import and run (no need to change directory)
        from hrmsystem.application import create_app

        app = create_app()
        app.run(host=host, port=port, debug=debug)

    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Enhanced HRM System Startup')
    parser.add_argument('--host', default='127.0.0.1', help='Host to bind to')
    parser.add_argument('--port', type=int, default=5000, help='Port to bind to')
    parser.add_argument('--no-debug', action='store_true', help='Disable debug mode')
    parser.add_argument('--skip-checks', action='store_true', help='Skip dependency and security checks')
    parser.add_argument('--test-first', action='store_true', help='Run tests before starting')
    
    args = parser.parse_args()
    
    print_banner()
    
    if not args.skip_checks:
        # Run all checks
        if not check_dependencies():
            print("❌ Dependency check failed")
            sys.exit(1)
        
        setup_environment()
        create_directories()
        
        if not initialize_database():
            print("❌ Database initialization failed")
            sys.exit(1)
        
        run_security_check()
    
    if args.test_first:
        print("🧪 Running tests first...")
        try:
            result = subprocess.run([sys.executable, 'run_tests.py', '--type', 'fast'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Tests passed")
            else:
                print("❌ Some tests failed")
                print(result.stdout)
                print(result.stderr)
                response = input("Continue anyway? (y/N): ")
                if response.lower() != 'y':
                    sys.exit(1)
        except Exception as e:
            print(f"⚠️  Could not run tests: {e}")
    
    # Start the application
    debug_mode = not args.no_debug
    start_application(args.host, args.port, debug_mode)


if __name__ == '__main__':
    main()
