"""
API Routes for HRM System
"""
from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from ..models import Employee, LeaveRequest, User, Permission, EmployeeStatus, LeaveStatus
from .. import db
from .auth import token_required, permission_required, generate_token
from datetime import datetime, date
import json

api_bp = Blueprint('api', __name__)


# Authentication endpoints
@api_bp.route('/auth/login', methods=['POST'])
def api_login():
    """API login endpoint to get access token."""
    data = request.get_json()
    
    if not data or not data.get('username') or not data.get('password'):
        return jsonify({'message': 'Username and password required'}), 400
    
    user = User.query.filter_by(username=data['username']).first()
    
    if user and user.check_password(data['password']):
        token = generate_token(user.id)
        return jsonify({
            'message': 'Login successful',
            'token': token,
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'role': user.role.value if user.role else None,
                'full_name': user.full_name
            }
        }), 200
    
    return jsonify({'message': 'Invalid credentials'}), 401


# Employee endpoints
@api_bp.route('/employees', methods=['GET'])
@token_required
@permission_required(Permission.VIEW_EMPLOYEES)
def api_get_employees(current_user_id):
    """Get list of employees."""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    search = request.args.get('search', '')
    status = request.args.get('status', '')
    unit = request.args.get('unit', '')
    
    query = Employee.query
    
    # Apply filters
    if search:
        query = query.filter(
            (Employee.name.contains(search)) |
            (Employee.military_id.contains(search)) |
            (Employee.national_id.contains(search))
        )
    
    if status:
        try:
            status_enum = EmployeeStatus[status]
            query = query.filter(Employee.status == status_enum)
        except KeyError:
            pass
    
    if unit:
        query = query.filter(Employee.unit.contains(unit))
    
    # Paginate results
    employees = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'employees': [{
            'id': emp.id,
            'military_id': emp.military_id,
            'name': emp.name,
            'national_id': emp.national_id,
            'blood_type': emp.blood_type,
            'military_rank': emp.military_rank,
            'unit': emp.unit,
            'position': emp.position,
            'status': emp.status.value if emp.status else None,
            'phone': emp.phone,
            'email': emp.email,
            'hire_date': emp.hire_date.isoformat() if emp.hire_date else None,
            'created_at': emp.created_at.isoformat() if emp.created_at else None
        } for emp in employees.items],
        'pagination': {
            'page': employees.page,
            'pages': employees.pages,
            'per_page': employees.per_page,
            'total': employees.total,
            'has_next': employees.has_next,
            'has_prev': employees.has_prev
        }
    }), 200


@api_bp.route('/employees/<int:employee_id>', methods=['GET'])
@token_required
@permission_required(Permission.VIEW_EMPLOYEES)
def api_get_employee(current_user_id, employee_id):
    """Get specific employee details."""
    employee = Employee.query.get_or_404(employee_id)
    
    return jsonify({
        'employee': {
            'id': employee.id,
            'military_id': employee.military_id,
            'name': employee.name,
            'national_id': employee.national_id,
            'blood_type': employee.blood_type,
            'date_of_birth': employee.date_of_birth.isoformat() if employee.date_of_birth else None,
            'birth_place': employee.birth_place,
            'current_residence': employee.current_residence,
            'education': employee.education,
            'education_date': employee.education_date.isoformat() if employee.education_date else None,
            'category': employee.category.value if employee.category else None,
            'bank_name': employee.bank_name,
            'bank_account': employee.bank_account,
            'military_rank': employee.military_rank,
            'unit': employee.unit,
            'position': employee.position,
            'status': employee.status.value if employee.status else None,
            'status_notes': employee.status_notes,
            'phone': employee.phone,
            'email': employee.email,
            'leave_balance': employee.leave_balance,
            'hire_date': employee.hire_date.isoformat() if employee.hire_date else None,
            'last_promotion_date': employee.last_promotion_date.isoformat() if employee.last_promotion_date else None,
            'created_at': employee.created_at.isoformat() if employee.created_at else None,
            'updated_at': employee.updated_at.isoformat() if employee.updated_at else None
        }
    }), 200


@api_bp.route('/employees', methods=['POST'])
@token_required
@permission_required(Permission.ADD_EMPLOYEE)
def api_create_employee(current_user_id):
    """Create new employee."""
    data = request.get_json()
    
    if not data or not data.get('military_id') or not data.get('name'):
        return jsonify({'message': 'Military ID and name are required'}), 400
    
    # Check if military ID already exists
    existing_employee = Employee.query.filter_by(military_id=data['military_id']).first()
    if existing_employee:
        return jsonify({'message': 'Military ID already exists'}), 400
    
    try:
        employee = Employee(
            military_id=data['military_id'],
            name=data['name'],
            national_id=data.get('national_id'),
            blood_type=data.get('blood_type'),
            military_rank=data.get('military_rank'),
            unit=data.get('unit'),
            position=data.get('position'),
            phone=data.get('phone'),
            email=data.get('email')
        )
        
        # Handle date fields
        if data.get('date_of_birth'):
            employee.date_of_birth = datetime.strptime(data['date_of_birth'], '%Y-%m-%d').date()
        if data.get('hire_date'):
            employee.hire_date = datetime.strptime(data['hire_date'], '%Y-%m-%d').date()
        
        db.session.add(employee)
        db.session.commit()
        
        return jsonify({
            'message': 'Employee created successfully',
            'employee_id': employee.id
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error creating employee: {str(e)}'}), 500


# Leave endpoints
@api_bp.route('/leaves', methods=['GET'])
@token_required
@permission_required(Permission.VIEW_LEAVES)
def api_get_leaves(current_user_id):
    """Get list of leave requests."""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    status = request.args.get('status', '')
    employee_id = request.args.get('employee_id', type=int)
    
    query = LeaveRequest.query
    
    # Apply filters
    if status:
        try:
            status_enum = LeaveStatus[status]
            query = query.filter(LeaveRequest.status == status_enum)
        except KeyError:
            pass
    
    if employee_id:
        query = query.filter(LeaveRequest.employee_id == employee_id)
    
    # Paginate results
    leaves = query.order_by(LeaveRequest.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'leaves': [{
            'id': leave.id,
            'employee_id': leave.employee_id,
            'employee_name': leave.employee.name if leave.employee else None,
            'start_date': leave.start_date.isoformat() if leave.start_date else None,
            'end_date': leave.end_date.isoformat() if leave.end_date else None,
            'total_days': leave.total_days,
            'reason': leave.reason,
            'status': leave.status.value if leave.status else None,
            'approved_by': leave.approved_by,
            'approved_at': leave.approved_at.isoformat() if leave.approved_at else None,
            'rejection_reason': leave.rejection_reason,
            'created_at': leave.created_at.isoformat() if leave.created_at else None
        } for leave in leaves.items],
        'pagination': {
            'page': leaves.page,
            'pages': leaves.pages,
            'per_page': leaves.per_page,
            'total': leaves.total,
            'has_next': leaves.has_next,
            'has_prev': leaves.has_prev
        }
    }), 200


@api_bp.route('/leaves/<int:leave_id>/approve', methods=['POST'])
@token_required
@permission_required(Permission.APPROVE_LEAVE)
def api_approve_leave(current_user_id, leave_id):
    """Approve a leave request."""
    leave_request = LeaveRequest.query.get_or_404(leave_id)
    
    if leave_request.status != LeaveStatus.PENDING:
        return jsonify({'message': 'Leave request is not pending'}), 400
    
    try:
        leave_request.status = LeaveStatus.APPROVED
        leave_request.approved_by = current_user_id
        leave_request.approved_at = datetime.now()
        
        db.session.commit()
        
        return jsonify({'message': 'Leave request approved successfully'}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error approving leave: {str(e)}'}), 500


@api_bp.route('/leaves/<int:leave_id>/reject', methods=['POST'])
@token_required
@permission_required(Permission.REJECT_LEAVE)
def api_reject_leave(current_user_id, leave_id):
    """Reject a leave request."""
    leave_request = LeaveRequest.query.get_or_404(leave_id)
    data = request.get_json()
    
    if leave_request.status != LeaveStatus.PENDING:
        return jsonify({'message': 'Leave request is not pending'}), 400
    
    try:
        leave_request.status = LeaveStatus.REJECTED
        leave_request.rejection_reason = data.get('rejection_reason', '')
        
        db.session.commit()
        
        return jsonify({'message': 'Leave request rejected successfully'}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'Error rejecting leave: {str(e)}'}), 500


# Statistics endpoints
@api_bp.route('/stats/dashboard', methods=['GET'])
@token_required
def api_dashboard_stats(current_user_id):
    """Get dashboard statistics."""
    total_employees = Employee.query.count()
    active_employees = Employee.query.filter_by(status=EmployeeStatus.ACTIVE).count()
    pending_leaves = LeaveRequest.query.filter_by(status=LeaveStatus.PENDING).count()
    approved_leaves = LeaveRequest.query.filter_by(status=LeaveStatus.APPROVED).count()
    
    return jsonify({
        'stats': {
            'total_employees': total_employees,
            'active_employees': active_employees,
            'pending_leaves': pending_leaves,
            'approved_leaves': approved_leaves
        }
    }), 200
