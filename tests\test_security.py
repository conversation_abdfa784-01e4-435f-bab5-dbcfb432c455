"""
Security tests for HRM System
"""
import pytest
import time
from hrmsystem.application.security.validation import (
    validate_military_id, validate_national_id, validate_email,
    validate_phone, validate_password, validate_employee_data
)
from hrmsystem.application.security.rate_limiting import RateLimiter


class TestValidation:
    """Test input validation functions."""
    
    def test_validate_military_id_valid(self):
        """Test valid military ID validation."""
        valid, msg = validate_military_id('12345')
        assert valid is True
        assert msg == ""
    
    def test_validate_military_id_invalid_empty(self):
        """Test empty military ID validation."""
        valid, msg = validate_military_id('')
        assert valid is False
        assert 'مطلوب' in msg
    
    def test_validate_military_id_invalid_non_numeric(self):
        """Test non-numeric military ID validation."""
        valid, msg = validate_military_id('ABC123')
        assert valid is False
        assert 'أرقام فقط' in msg
    
    def test_validate_military_id_invalid_length(self):
        """Test military ID with invalid length."""
        valid, msg = validate_military_id('12')  # Too short
        assert valid is False
        assert 'بين 3 و 10' in msg
        
        valid, msg = validate_military_id('12345678901')  # Too long
        assert valid is False
        assert 'بين 3 و 10' in msg
    
    def test_validate_national_id_valid(self):
        """Test valid national ID validation."""
        valid, msg = validate_national_id('1234567890')
        assert valid is True
        assert msg == ""
    
    def test_validate_national_id_invalid_length(self):
        """Test national ID with invalid length."""
        valid, msg = validate_national_id('123456789')  # Too short
        assert valid is False
        assert '10 أرقام' in msg
    
    def test_validate_national_id_invalid_start(self):
        """Test national ID with invalid starting digit."""
        valid, msg = validate_national_id('3234567890')
        assert valid is False
        assert 'يبدأ بـ 1 أو 2' in msg
    
    def test_validate_email_valid(self):
        """Test valid email validation."""
        valid, msg = validate_email('<EMAIL>')
        assert valid is True
        assert msg == ""
    
    def test_validate_email_invalid(self):
        """Test invalid email validation."""
        valid, msg = validate_email('invalid-email')
        assert valid is False
        assert 'صيغة البريد الإلكتروني' in msg
    
    def test_validate_phone_valid(self):
        """Test valid phone number validation."""
        valid, msg = validate_phone('0501234567')
        assert valid is True
        assert msg == ""
        
        valid, msg = validate_phone('+966501234567')
        assert valid is True
        assert msg == ""
    
    def test_validate_phone_invalid(self):
        """Test invalid phone number validation."""
        valid, msg = validate_phone('1234567890')  # Not Saudi format
        assert valid is False
        assert 'رقم سعودي صحيح' in msg
    
    def test_validate_password_valid(self):
        """Test valid password validation."""
        valid, msg = validate_password('password123')
        assert valid is True
        assert msg == ""
    
    def test_validate_password_too_short(self):
        """Test password that's too short."""
        valid, msg = validate_password('pass1')
        assert valid is False
        assert '8 أحرف' in msg
    
    def test_validate_password_no_digit(self):
        """Test password without digits."""
        valid, msg = validate_password('password')
        assert valid is False
        assert 'رقم واحد' in msg
    
    def test_validate_password_no_letter(self):
        """Test password without letters."""
        valid, msg = validate_password('12345678')
        assert valid is False
        assert 'حرف واحد' in msg
    
    def test_validate_employee_data_valid(self):
        """Test valid employee data validation."""
        data = {
            'military_id': '12345',
            'name': 'أحمد محمد',
            'national_id': '1234567890',
            'email': '<EMAIL>',
            'phone': '0501234567',
            'blood_type': 'O+'
        }
        
        errors = validate_employee_data(data)
        assert len(errors) == 0
    
    def test_validate_employee_data_invalid(self):
        """Test invalid employee data validation."""
        data = {
            'military_id': '',  # Invalid
            'name': 'أ',  # Too short
            'national_id': '123',  # Invalid
            'email': 'invalid-email',  # Invalid
            'phone': '123',  # Invalid
            'blood_type': 'XY'  # Invalid
        }
        
        errors = validate_employee_data(data)
        assert len(errors) > 0
        assert 'military_id' in errors
        assert 'name' in errors
        assert 'national_id' in errors
        assert 'email' in errors
        assert 'phone' in errors
        assert 'blood_type' in errors


class TestRateLimiting:
    """Test rate limiting functionality."""
    
    def test_rate_limiter_allows_requests_within_limit(self):
        """Test that rate limiter allows requests within limit."""
        limiter = RateLimiter()
        key = "test_key"
        limit = 5
        window = 60
        
        # Should allow first 5 requests
        for i in range(limit):
            assert limiter.is_allowed(key, limit, window) is True
    
    def test_rate_limiter_blocks_requests_over_limit(self):
        """Test that rate limiter blocks requests over limit."""
        limiter = RateLimiter()
        key = "test_key"
        limit = 3
        window = 60
        
        # Allow first 3 requests
        for i in range(limit):
            assert limiter.is_allowed(key, limit, window) is True
        
        # Block 4th request
        assert limiter.is_allowed(key, limit, window) is False
    
    def test_rate_limiter_resets_after_window(self):
        """Test that rate limiter resets after time window."""
        limiter = RateLimiter()
        key = "test_key"
        limit = 2
        window = 1  # 1 second window
        
        # Use up the limit
        assert limiter.is_allowed(key, limit, window) is True
        assert limiter.is_allowed(key, limit, window) is True
        assert limiter.is_allowed(key, limit, window) is False
        
        # Wait for window to expire
        time.sleep(1.1)
        
        # Should allow requests again
        assert limiter.is_allowed(key, limit, window) is True
    
    def test_rate_limiter_different_keys(self):
        """Test that rate limiter treats different keys separately."""
        limiter = RateLimiter()
        limit = 2
        window = 60
        
        # Use up limit for key1
        assert limiter.is_allowed("key1", limit, window) is True
        assert limiter.is_allowed("key1", limit, window) is True
        assert limiter.is_allowed("key1", limit, window) is False
        
        # key2 should still be allowed
        assert limiter.is_allowed("key2", limit, window) is True
        assert limiter.is_allowed("key2", limit, window) is True
        assert limiter.is_allowed("key2", limit, window) is False


class TestSecurityHeaders:
    """Test security headers and CSRF protection."""
    
    def test_csrf_protection_enabled(self, client):
        """Test that CSRF protection is enabled for forms."""
        # This would need to be implemented based on your CSRF setup
        # For now, just test that the login page loads
        response = client.get('/auth/login')
        assert response.status_code == 200
        # In a real test, you'd check for CSRF tokens in the form
    
    def test_secure_headers_present(self, client):
        """Test that security headers are present in responses."""
        response = client.get('/auth/login')
        
        # Check for security headers (you may need to implement these)
        # assert 'X-Content-Type-Options' in response.headers
        # assert 'X-Frame-Options' in response.headers
        # assert 'X-XSS-Protection' in response.headers
        
        # For now, just verify the response is successful
        assert response.status_code == 200


class TestInputSanitization:
    """Test input sanitization and XSS prevention."""
    
    def test_html_sanitization(self):
        """Test HTML input sanitization."""
        from hrmsystem.application.security.validation import sanitize_input
        
        # Test basic sanitization
        dirty_input = "<script>alert('xss')</script>Hello"
        clean_input = sanitize_input(dirty_input)
        assert '<script>' not in clean_input
        assert 'Hello' in clean_input
    
    def test_allowed_html_tags(self):
        """Test that allowed HTML tags are preserved."""
        from hrmsystem.application.security.validation import sanitize_input
        
        # Test allowed tags
        input_with_allowed_tags = "<b>Bold</b> and <i>italic</i> text"
        clean_input = sanitize_input(input_with_allowed_tags)
        assert '<b>' in clean_input
        assert '<i>' in clean_input
    
    def test_dangerous_attributes_removed(self):
        """Test that dangerous attributes are removed."""
        from hrmsystem.application.security.validation import sanitize_input
        
        # Test dangerous attributes
        dangerous_input = '<a href="javascript:alert(1)">Link</a>'
        clean_input = sanitize_input(dangerous_input)
        assert 'javascript:' not in clean_input


class TestPasswordSecurity:
    """Test password security features."""
    
    def test_password_hashing(self, app):
        """Test that passwords are properly hashed."""
        from hrmsystem.application.models import User
        
        with app.app_context():
            user = User(username='test', email='<EMAIL>')
            user.set_password('testpassword')
            
            # Password should be hashed
            assert user.password != 'testpassword'
            assert len(user.password) > 20  # Hashed passwords are longer
            
            # Should be able to verify password
            assert user.check_password('testpassword') is True
            assert user.check_password('wrongpassword') is False


class TestPermissionSystem:
    """Test permission and authorization system."""
    
    def test_admin_has_all_permissions(self, app, admin_user):
        """Test that admin user has all permissions."""
        with app.app_context():
            from hrmsystem.application.models import Permission
            
            # Admin should have all permissions
            for permission in Permission:
                assert admin_user.has_permission(permission)
    
    def test_regular_user_limited_permissions(self, app, regular_user):
        """Test that regular user has limited permissions."""
        with app.app_context():
            from hrmsystem.application.models import Permission
            
            # Regular user should not have admin permissions
            assert not regular_user.has_permission(Permission.DELETE_EMPLOYEE)
            assert not regular_user.has_permission(Permission.MANAGE_USERS)
            assert not regular_user.has_permission(Permission.MANAGE_SYSTEM)
