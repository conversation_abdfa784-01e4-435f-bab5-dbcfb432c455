"""
API Authentication utilities
"""
from functools import wraps
from flask import request, jsonify, current_app
from flask_login import current_user
import jwt
from datetime import datetime, timedelta


def token_required(f):
    """Decorator to require API token authentication."""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        
        # Check for token in header
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            try:
                token = auth_header.split(" ")[1]  # Bearer <token>
            except IndexError:
                return jsonify({'message': 'Invalid token format'}), 401
        
        if not token:
            return jsonify({'message': 'Token is missing'}), 401
        
        try:
            # Decode the token
            data = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user_id = data['user_id']
            
            # You can add additional user validation here
            from ..models import User
            user = User.query.get(current_user_id)
            if not user:
                return jsonify({'message': 'Invalid token'}), 401
                
        except jwt.ExpiredSignatureError:
            return jsonify({'message': 'Token has expired'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'message': 'Invalid token'}), 401
        
        return f(current_user_id, *args, **kwargs)
    
    return decorated


def generate_token(user_id, expires_in=3600):
    """Generate JWT token for API access."""
    payload = {
        'user_id': user_id,
        'exp': datetime.utcnow() + timedelta(seconds=expires_in),
        'iat': datetime.utcnow()
    }
    
    token = jwt.encode(payload, current_app.config['SECRET_KEY'], algorithm='HS256')
    return token


def permission_required(permission):
    """Decorator to require specific permission for API access."""
    def decorator(f):
        @wraps(f)
        def decorated(current_user_id, *args, **kwargs):
            from ..models import User
            user = User.query.get(current_user_id)
            
            if not user or not user.has_permission(permission):
                return jsonify({'message': 'Insufficient permissions'}), 403
            
            return f(current_user_id, *args, **kwargs)
        return decorated
    return decorator
