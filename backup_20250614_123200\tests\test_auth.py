"""
Authentication tests for HRM System
"""
import pytest
from flask import url_for
from hrmsystem.application.models import User, UserRole


class TestAuth:
    """Test authentication functionality."""
    
    def test_login_page_loads(self, client):
        """Test that login page loads correctly."""
        response = client.get('/auth/login')
        assert response.status_code == 200
        assert 'تسجيل الدخول' in response.get_data(as_text=True)
    
    def test_valid_login(self, client, admin_user):
        """Test login with valid credentials."""
        response = client.post('/auth/login', data={
            'username': 'admin_test',
            'password': 'admin123'
        }, follow_redirects=True)
        
        assert response.status_code == 200
        # Should redirect to dashboard after successful login
        assert 'لوحة التحكم' in response.get_data(as_text=True)
    
    def test_invalid_login_wrong_password(self, client, admin_user):
        """Test login with wrong password."""
        response = client.post('/auth/login', data={
            'username': 'admin_test',
            'password': 'wrongpassword'
        })
        
        assert response.status_code == 200
        assert 'كلمة المرور غير صحيحة' in response.get_data(as_text=True)
    
    def test_invalid_login_wrong_username(self, client):
        """Test login with non-existent username."""
        response = client.post('/auth/login', data={
            'username': 'nonexistent',
            'password': 'password'
        })
        
        assert response.status_code == 200
        assert 'اسم المستخدم غير موجود' in response.get_data(as_text=True)
    
    def test_logout(self, client, auth, admin_user):
        """Test logout functionality."""
        # First login
        auth.login()
        
        # Then logout
        response = auth.logout()
        assert response.status_code == 200
        assert 'تسجيل الدخول' in response.get_data(as_text=True)
    
    def test_login_required_redirect(self, client):
        """Test that protected pages redirect to login."""
        response = client.get('/dashboard/')
        assert response.status_code == 302
        assert '/auth/login' in response.location
    
    def test_password_hashing(self, app):
        """Test that passwords are properly hashed."""
        with app.app_context():
            user = User(
                username='test_hash',
                email='<EMAIL>',
                role=UserRole.USER
            )
            user.set_password('testpassword')
            
            # Password should be hashed, not stored in plain text
            assert user.password != 'testpassword'
            assert user.check_password('testpassword') is True
            assert user.check_password('wrongpassword') is False


class TestUserRegistration:
    """Test user registration functionality."""
    
    def test_register_page_loads(self, client):
        """Test that register page loads correctly."""
        response = client.get('/auth/register')
        assert response.status_code == 200
        assert 'إنشاء حساب جديد' in response.get_data(as_text=True)
    
    def test_valid_registration(self, client):
        """Test registration with valid data."""
        response = client.post('/auth/register', data={
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'newpassword123',
            'confirm_password': 'newpassword123',
            'full_name': 'مستخدم جديد'
        }, follow_redirects=True)
        
        assert response.status_code == 200
        # Should redirect to login after successful registration
        assert 'تم إنشاء الحساب بنجاح' in response.get_data(as_text=True)
    
    def test_duplicate_username_registration(self, client, admin_user):
        """Test registration with existing username."""
        response = client.post('/auth/register', data={
            'username': 'admin_test',  # Already exists
            'email': '<EMAIL>',
            'password': 'password123',
            'confirm_password': 'password123',
            'full_name': 'مستخدم آخر'
        })
        
        assert response.status_code == 200
        assert 'اسم المستخدم موجود بالفعل' in response.get_data(as_text=True)
    
    def test_duplicate_email_registration(self, client, admin_user):
        """Test registration with existing email."""
        response = client.post('/auth/register', data={
            'username': 'differentuser',
            'email': '<EMAIL>',  # Already exists
            'password': 'password123',
            'confirm_password': 'password123',
            'full_name': 'مستخدم آخر'
        })
        
        assert response.status_code == 200
        assert 'البريد الإلكتروني موجود بالفعل' in response.get_data(as_text=True)
    
    def test_password_mismatch_registration(self, client):
        """Test registration with mismatched passwords."""
        response = client.post('/auth/register', data={
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'password123',
            'confirm_password': 'differentpassword',
            'full_name': 'مستخدم تجريبي'
        })
        
        assert response.status_code == 200
        assert 'كلمات المرور غير متطابقة' in response.get_data(as_text=True)


class TestUserPermissions:
    """Test user permissions and roles."""
    
    def test_admin_permissions(self, app, admin_user):
        """Test that admin user has all permissions."""
        with app.app_context():
            from hrmsystem.application.models import Permission
            
            # Admin should have all permissions
            for permission in Permission:
                assert admin_user.has_permission(permission)
    
    def test_regular_user_permissions(self, app, regular_user):
        """Test that regular user has limited permissions."""
        with app.app_context():
            from hrmsystem.application.models import Permission
            
            # Regular user should have no permissions by default
            assert not regular_user.has_permission(Permission.DELETE_EMPLOYEE)
            assert not regular_user.has_permission(Permission.MANAGE_USERS)
    
    def test_supervisor_permissions(self, app):
        """Test supervisor permissions."""
        with app.app_context():
            from hrmsystem.application.models import Permission
            
            supervisor = User(
                username='supervisor_test',
                email='<EMAIL>',
                role=UserRole.SUPERVISOR,
                full_name='مشرف الاختبار'
            )
            
            # Supervisor should have specific permissions
            assert supervisor.has_permission(Permission.VIEW_EMPLOYEES)
            assert supervisor.has_permission(Permission.APPROVE_LEAVE)
            assert not supervisor.has_permission(Permission.DELETE_EMPLOYEE)
