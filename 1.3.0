Defaulting to user installation because normal site-packages is not writeable
Collecting pytest-flask
  Downloading pytest_flask-1.3.0-py3-none-any.whl.metadata (14 kB)
Requirement already satisfied: pytest>=5.2 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pytest-flask) (8.4.0)
Requirement already satisfied: Flask in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pytest-flask) (2.3.3)
Requirement already satisfied: Werkzeug in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pytest-flask) (3.1.3)
Requirement already satisfied: colorama>=0.4 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pytest>=5.2->pytest-flask) (0.4.6)
Requirement already satisfied: iniconfig>=1 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pytest>=5.2->pytest-flask) (2.1.0)
Requirement already satisfied: packaging>=20 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pytest>=5.2->pytest-flask) (25.0)
Requirement already satisfied: pluggy<2,>=1.5 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pytest>=5.2->pytest-flask) (1.6.0)
Requirement already satisfied: pygments>=2.7.2 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pytest>=5.2->pytest-flask) (2.19.1)
Requirement already satisfied: Jinja2>=3.1.2 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from Flask->pytest-flask) (3.1.6)
Requirement already satisfied: itsdangerous>=2.1.2 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from Flask->pytest-flask) (2.2.0)
Requirement already satisfied: click>=8.1.3 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from Flask->pytest-flask) (8.2.1)
Requirement already satisfied: blinker>=1.6.2 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from Flask->pytest-flask) (1.9.0)
Requirement already satisfied: MarkupSafe>=2.1.1 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from Werkzeug->pytest-flask) (3.0.2)
Downloading pytest_flask-1.3.0-py3-none-any.whl (13 kB)
Installing collected packages: pytest-flask
Successfully installed pytest-flask-1.3.0
