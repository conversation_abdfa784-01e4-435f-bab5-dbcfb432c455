# اختبارات نظام إدارة الموارد البشرية

هذا المجلد يحتوي على جميع الاختبارات الخاصة بنظام إدارة الموارد البشرية.

## هيكل الاختبارات

```
tests/
├── __init__.py
├── conftest.py          # إعدادات الاختبارات المشتركة
├── test_auth.py         # اختبارات المصادقة
├── test_employees.py    # اختبارات إدارة الموظفين
├── test_leaves.py       # اختبارات إدارة الإجازات
├── test_api.py          # اختبارات API
├── test_security.py     # اختبارات الأمان
└── README.md           # هذا الملف
```

## تشغيل الاختبارات

### تثبيت المتطلبات
```bash
pip install pytest pytest-flask
```

### تشغيل جميع الاختبارات
```bash
pytest
```

### تشغيل اختبارات محددة
```bash
# اختبارات المصادقة فقط
pytest tests/test_auth.py

# اختبارات الموظفين فقط
pytest tests/test_employees.py

# اختبارات الإجازات فقط
pytest tests/test_leaves.py
```

### تشغيل اختبارات بعلامات محددة
```bash
# اختبارات الأمان فقط
pytest -m security

# اختبارات API فقط
pytest -m api

# تجاهل الاختبارات البطيئة
pytest -m "not slow"
```

### تشغيل الاختبارات مع تقرير التغطية
```bash
pip install pytest-cov
pytest --cov=hrmsystem --cov-report=html
```

## أنواع الاختبارات

### 1. اختبارات الوحدة (Unit Tests)
- تختبر وظائف محددة بشكل منفصل
- سريعة التنفيذ
- لا تتطلب قاعدة بيانات حقيقية

### 2. اختبارات التكامل (Integration Tests)
- تختبر التفاعل بين المكونات المختلفة
- تستخدم قاعدة بيانات اختبار
- أبطأ من اختبارات الوحدة

### 3. اختبارات API
- تختبر endpoints الخاصة بـ API
- تتضمن اختبارات المصادقة والتخويل
- تختبر صيغة JSON للاستجابات

### 4. اختبارات الأمان
- تختبر ميزات الأمان مثل Rate Limiting
- تختبر التحقق من الصلاحيات
- تختبر حماية من XSS و CSRF

## إعداد بيئة الاختبار

يتم إنشاء قاعدة بيانات مؤقتة لكل اختبار باستخدام SQLite في الذاكرة، مما يضمن:
- عزل الاختبارات عن بعضها البعض
- سرعة تنفيذ الاختبارات
- عدم تأثير الاختبارات على البيانات الحقيقية

## كتابة اختبارات جديدة

### مثال على اختبار بسيط:
```python
def test_employee_creation(authenticated_client, app):
    """Test creating a new employee."""
    response = authenticated_client.post('/employees/create', data={
        'military_id': '12345',
        'name': 'أحمد محمد',
        'military_rank': 'ملازم'
    })
    
    assert response.status_code == 302  # Redirect after success
    
    # Verify in database
    with app.app_context():
        employee = Employee.query.filter_by(military_id='12345').first()
        assert employee is not None
        assert employee.name == 'أحمد محمد'
```

### استخدام Fixtures:
```python
def test_with_sample_data(client, admin_user, sample_employee):
    """Test using predefined fixtures."""
    # admin_user و sample_employee متوفران من conftest.py
    assert admin_user.is_admin
    assert sample_employee.name is not None
```

## أفضل الممارسات

1. **اسماء وصفية**: استخدم أسماء وصفية للاختبارات
2. **اختبار واحد لكل وظيفة**: كل اختبار يجب أن يختبر شيء واحد فقط
3. **ترتيب AAA**: Arrange, Act, Assert
4. **استخدام Fixtures**: لتجهيز البيانات المشتركة
5. **تنظيف البيانات**: تأكد من تنظيف البيانات بعد كل اختبار

## تشغيل الاختبارات في CI/CD

يمكن دمج الاختبارات في pipeline التطوير:

```yaml
# مثال GitHub Actions
- name: Run tests
  run: |
    pip install -r requirements.txt
    pip install pytest pytest-flask pytest-cov
    pytest --cov=hrmsystem --cov-report=xml
```

## استكشاف الأخطاء

### مشاكل شائعة:
1. **فشل الاتصال بقاعدة البيانات**: تأكد من إعدادات الاختبار
2. **فشل استيراد الوحدات**: تأكد من PYTHONPATH
3. **فشل CSRF**: تم تعطيله في إعدادات الاختبار

### تشغيل اختبار واحد للتشخيص:
```bash
pytest tests/test_auth.py::TestAuth::test_valid_login -v -s
```
