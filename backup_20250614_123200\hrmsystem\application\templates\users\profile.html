{% extends 'layouts/base.html' %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-user-circle"></i> الملف الشخصي</h2>
        <a href="{{ url_for('users.change_password') }}" class="btn btn-warning">
            <i class="fas fa-key"></i> تغيير كلمة المرور
        </a>
    </div>
    
    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user"></i> البيانات الشخصية</h5>
                </div>
                <div class="card-body text-center">
                    {% if current_user.profile_image %}
                    <img src="{{ url_for('static', filename='img/users/' + current_user.profile_image) }}" alt="{{ current_user.username }}" class="img-thumbnail rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                    {% else %}
                    <img src="{{ url_for('static', filename='img/default_profile.png') }}" alt="{{ current_user.username }}" class="img-thumbnail rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                    {% endif %}
                    <h4>{{ current_user.full_name or current_user.username }}</h4>
                    <p class="text-muted">{{ current_user.username }}</p>
                    
                    <div class="mt-3">
                        {% if current_user.role.name == 'ADMIN' %}
                        <span class="badge bg-danger">{{ current_user.role.value }}</span>
                        {% elif current_user.role.name == 'SUPERVISOR' %}
                        <span class="badge bg-warning">{{ current_user.role.value }}</span>
                        {% else %}
                        <span class="badge bg-info">{{ current_user.role.value }}</span>
                        {% endif %}
                    </div>
                </div>
                <ul class="list-group list-group-flush">
                    <li class="list-group-item">
                        <i class="fas fa-envelope text-primary"></i> البريد الإلكتروني: {{ current_user.email }}
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-phone text-primary"></i> رقم الهاتف: {{ current_user.phone or 'غير محدد' }}
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-calendar-alt text-primary"></i> تاريخ الإنشاء: {{ current_user.created_at.strftime('%Y-%m-%d') }}
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-sign-in-alt text-primary"></i> آخر تسجيل دخول: 
                        {% if current_user.last_login %}
                        {{ current_user.last_login.strftime('%Y-%m-%d %H:%M') }}
                        {% else %}
                        لم يسجل الدخول بعد
                        {% endif %}
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-edit"></i> تعديل الملف الشخصي</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('users.profile') }}" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.full_name.label(class="form-label") }}
                            {% if form.full_name.errors %}
                                {{ form.full_name(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.full_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.full_name(class="form-control") }}
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.email.label(class="form-label") }}
                            {% if form.email.errors %}
                                {{ form.email(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.email(class="form-control") }}
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.phone.label(class="form-label") }}
                            {% if form.phone.errors %}
                                {{ form.phone(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.phone(class="form-control") }}
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.profile_image.label(class="form-label") }}
                            {% if form.profile_image.errors %}
                                {{ form.profile_image(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.profile_image.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.profile_image(class="form-control") }}
                            {% endif %}
                            <div class="form-text">يسمح فقط بملفات الصور (jpg, jpeg, png). اترك هذا الحقل فارغًا للاحتفاظ بالصورة الحالية.</div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
