{% extends 'layouts/base_print.html' %}

{% block title %}
كشف الموظفين حسب الحالة
{% endblock %}

{% block content %}
    <!-- أزرار التحكم (تظهر فقط في الشاشة وليس عند الطباعة) -->
    <div class="control-buttons d-flex justify-content-between mb-4 no-print">
        <div>
            <button class="btn-print">
                <i class="fas fa-print me-1"></i> طباعة
            </button>
            <a href="{{ url_for('reports.export_report', report_type='status', **request.args) }}" class="btn-export">
                <i class="fas fa-file-excel me-1"></i> تصدير Excel
            </a>
        </div>
        <button class="btn-back">
            <i class="fas fa-arrow-right me-1"></i> رجوع
        </button>
    </div>

    <!-- رأس التقرير -->
    <div class="report-header">
        <div class="report-title">
            كشف الموظفين حسب الحالة
        </div>
        <div class="report-subtitle">
            {{ unit_name|default('قسم المرور والتراخيص الخمس') }}
        </div>
    </div>

    <!-- جدول التقرير -->
    <table class="report-table report-status">
        <thead>
            <tr>
                <th width="5%">م</th>
                <th width="15%">الرقم العسكري</th>
                <th width="15%">الرتبة</th>
                <th width="45%">الاسم</th>
                <th width="10%">الرقم الوطني</th>
                <th width="10%">ملاحظات</th>
            </tr>
        </thead>
        <tbody>
            {% if employees %}
                {% for employee in employees %}
                <tr>
                    <td class="serial-number">{{ loop.index }}</td>
                    <td>{{ employee.military_id }}</td>
                    <td>{{ employee.military_rank }}</td>
                    <td style="text-align: right; font-weight: bold;">{{ employee.name }}</td>
                    <td>{{ employee.national_id }}</td>
                    <td>{{ employee.status_notes }}</td>
                </tr>
                {% endfor %}
            {% else %}
                {% for i in range(1, 11) %}
                <tr>
                    <td class="serial-number">{{ i }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                {% endfor %}
            {% endif %}
        </tbody>
    </table>

    <!-- تذييل التقرير -->
    <div class="report-footer">
        <div>
            <p style="font-weight: bold; font-size: 16pt;">يعتمد</p>
            <div style="margin-top: 40px; border-top: 1px solid #ff8f00; width: 150px;"></div>
        </div>
        <div>
            <p style="font-weight: bold;">التاريخ: {{ now.strftime('%Y/%m/%d') }}</p>
            <p>اسم المستخدم: {{ current_user.full_name or current_user.username }}</p>
        </div>
    </div>

    <!-- رقم الصفحة -->
    <div class="page-number">
        صفحة {{ page_number|default('1') }}
    </div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // طباعة التقرير
        document.querySelector('.btn-print').addEventListener('click', function() {
            window.print();
        });

        // رجوع للصفحة السابقة
        document.querySelector('.btn-back').addEventListener('click', function() {
            window.history.back();
        });
    });
</script>
{% endblock %}
