{% extends 'layouts/base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-history"></i> سجل تغييرات المستخدمين</h2>
        <a href="{{ url_for('users.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة إلى قائمة المستخدمين
        </a>
    </div>

    <!-- Search Form -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-filter"></i> تصفية النتائج</h5>
        </div>
        <div class="card-body">
            <form id="audit-log-filter-form" method="GET" action="{{ url_for('users.audit_logs') }}">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="user_id" class="form-label">المستخدم</label>
                        <select name="user_id" id="user_id" class="form-select">
                            <option value="">الكل</option>
                            {% for user in users %}
                            <option value="{{ user.id }}" {% if user_id == user.id %}selected{% endif %}>{{ user.username }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="action" class="form-label">النشاط</label>
                        <select name="action" id="action" class="form-select">
                            <option value="">الكل</option>
                            {% for act in actions %}
                            <option value="{{ act }}" {% if action == act %}selected{% endif %}>
                                {% if act == 'create' %}
                                إنشاء حساب
                                {% elif act == 'update' %}
                                تحديث البيانات
                                {% elif act == 'delete' %}
                                حذف الحساب
                                {% elif act == 'login' %}
                                تسجيل دخول
                                {% elif act == 'logout' %}
                                تسجيل خروج
                                {% elif act == 'password_change' %}
                                تغيير كلمة المرور
                                {% elif act == 'profile_update' %}
                                تحديث الملف الشخصي
                                {% else %}
                                {{ act }}
                                {% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="text-center">
                    <button type="submit" class="btn btn-primary">تصفية</button>
                    <a href="{{ url_for('users.audit_logs') }}" class="btn btn-secondary">إعادة تعيين</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Audit Logs List -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-list"></i> سجل التغييرات</h5>
        </div>
        <div class="card-body">
            {% if logs.items %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>المستخدم</th>
                            <th>النشاط</th>
                            <th>بواسطة</th>
                            <th>عنوان IP</th>
                            <th>التفاصيل</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in logs.items %}
                        <tr>
                            <td>{{ log.timestamp.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                <a href="{{ url_for('users.view', id=log.user_id) }}">{{ log.user.username }}</a>
                            </td>
                            <td>
                                {% if log.action == 'create' %}
                                <span class="badge bg-success">إنشاء حساب</span>
                                {% elif log.action == 'update' %}
                                <span class="badge bg-warning">تحديث البيانات</span>
                                {% elif log.action == 'delete' %}
                                <span class="badge bg-danger">حذف الحساب</span>
                                {% elif log.action == 'login' %}
                                <span class="badge bg-info">تسجيل دخول</span>
                                {% elif log.action == 'logout' %}
                                <span class="badge bg-secondary">تسجيل خروج</span>
                                {% elif log.action == 'password_change' %}
                                <span class="badge bg-primary">تغيير كلمة المرور</span>
                                {% elif log.action == 'profile_update' %}
                                <span class="badge bg-info">تحديث الملف الشخصي</span>
                                {% else %}
                                <span class="badge bg-dark">{{ log.action }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('users.view', id=log.action_by) }}">{{ log.actor.username }}</a>
                            </td>
                            <td>{{ log.ip_address }}</td>
                            <td>
                                {% if log.action == 'update' and log.old_values and log.new_values %}
                                <button class="btn btn-sm btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#changes{{ log.id }}" aria-expanded="false">
                                    عرض التغييرات
                                </button>
                                <div class="collapse mt-2" id="changes{{ log.id }}">
                                    <div class="card card-body">
                                        <h6>القيم القديمة:</h6>
                                        <pre class="mb-3">{{ log.old_values }}</pre>
                                        <h6>القيم الجديدة:</h6>
                                        <pre>{{ log.new_values }}</pre>
                                    </div>
                                </div>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if logs.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('users.audit_logs', page=logs.prev_num, user_id=user_id, action=action) }}">السابق</a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">السابق</a>
                    </li>
                    {% endif %}

                    {% for page_num in logs.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                        {% if page_num %}
                            {% if page_num == logs.page %}
                            <li class="page-item active" aria-current="page">
                                <a class="page-link" href="{{ url_for('users.audit_logs', page=page_num, user_id=user_id, action=action) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('users.audit_logs', page=page_num, user_id=user_id, action=action) }}">{{ page_num }}</a>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#">...</a>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if logs.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('users.audit_logs', page=logs.next_num, user_id=user_id, action=action) }}">التالي</a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% else %}
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle fa-2x mb-3"></i>
                <p class="mb-0">لا توجد سجلات نشاط متطابقة مع معايير البحث.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
