{"timestamp": "2025-06-14T12:32:01.526671", "backup_location": "backup_20250614_123200", "errors_found": [], "fixes_applied": [], "files_cleaned": [".\\__pycache__", ".\\application\\__pycache__", ".\\application\\audit\\__pycache__", ".\\application\\auth\\__pycache__", ".\\application\\dashboard\\__pycache__", ".\\application\\employees\\__pycache__", ".\\application\\leaves\\__pycache__", ".\\application\\notifications\\__pycache__", ".\\application\\reports\\__pycache__", ".\\backup_20250614_123200\\hrmsystem\\__pycache__", ".\\backup_20250614_123200\\hrmsystem\\application\\__pycache__", ".\\backup_20250614_123200\\hrmsystem\\application\\api\\__pycache__", ".\\backup_20250614_123200\\hrmsystem\\application\\auth\\__pycache__", ".\\backup_20250614_123200\\hrmsystem\\application\\dashboard\\__pycache__", ".\\backup_20250614_123200\\hrmsystem\\application\\employees\\__pycache__", ".\\backup_20250614_123200\\hrmsystem\\application\\leaves\\__pycache__", ".\\backup_20250614_123200\\hrmsystem\\application\\reports\\__pycache__", ".\\backup_20250614_123200\\hrmsystem\\application\\security\\__pycache__", ".\\backup_20250614_123200\\hrmsystem\\application\\users\\__pycache__", ".\\backup_20250614_123200\\tests\\__pycache__", ".\\hrmsystem\\__pycache__", ".\\hrmsystem\\application\\__pycache__", ".\\hrmsystem\\application\\api\\__pycache__", ".\\hrmsystem\\application\\auth\\__pycache__", ".\\hrmsystem\\application\\dashboard\\__pycache__", ".\\hrmsystem\\application\\employees\\__pycache__", ".\\hrmsystem\\application\\leaves\\__pycache__", ".\\hrmsystem\\application\\reports\\__pycache__", ".\\hrmsystem\\application\\security\\__pycache__", ".\\hrmsystem\\application\\users\\__pycache__", ".\\migrations\\__pycache__", ".\\tests\\__pycache__"], "improvements_made": [], "summary": {"total_errors": 0, "total_fixes": 0, "total_cleaned": 32, "total_improvements": 0}}