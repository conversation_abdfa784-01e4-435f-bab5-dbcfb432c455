{% extends 'layouts/base.html' %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-user"></i> بيانات الموظف</h2>
        <div>
            {% if current_user.has_permission(Permission.EDIT_EMPLOYEE) %}
            <a href="{{ url_for('employees.edit', id=employee.id) }}" class="btn btn-warning">
                <i class="fas fa-edit"></i> تعديل
            </a>
            {% endif %}
            {% if current_user.has_permission(Permission.PRINT_EMPLOYEE) %}
            <a href="{{ url_for('employees.print_employee', id=employee.id) }}" class="btn btn-info">
                <i class="fas fa-print"></i> طباعة
            </a>
            {% endif %}
            {% if current_user.has_permission(Permission.DELETE_EMPLOYEE) %}
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                <i class="fas fa-trash"></i> حذف
            </button>
            {% endif %}
            <a href="{{ url_for('employees.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة إلى قائمة الموظفين
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-id-card"></i> البطاقة الشخصية</h5>
                </div>
                <div class="card-body text-center">
                    {% if employee.profile_image %}
                    <img src="{{ url_for('static', filename='img/employees/' + employee.profile_image) }}" alt="{{ employee.name }}" class="employee-profile-image mb-3">
                    {% else %}
                    <img src="{{ url_for('static', filename='img/default_profile.png') }}" alt="{{ employee.name }}" class="employee-profile-image mb-3">
                    {% endif %}

                    <h4 class="mb-1">{{ employee.name }}</h4>
                    <p class="text-muted mb-3">{{ employee.military_rank or '' }}</p>

                    <div class="mb-3">
                        <span class="badge
                            {% if employee.status.name == 'ACTIVE' %}bg-success
                            {% elif employee.status.name == 'ABSENT' %}bg-danger
                            {% elif employee.status.name == 'ASSIGNED' %}bg-info
                            {% elif employee.status.name == 'ON_LEAVE' %}bg-warning
                            {% elif employee.status.name == 'SUSPENDED' %}bg-secondary
                            {% elif employee.status.name == 'SCATTERED' %}bg-primary
                            {% elif employee.status.name == 'MEDICAL' %}bg-dark
                            {% endif %}">
                            {{ employee.status.value }}
                        </span>
                    </div>

                    <div class="qr-code-container">
                        <img src="{{ qr_code }}" alt="QR Code" class="img-fluid">
                        <p class="text-muted small mt-2">امسح الرمز لعرض بيانات الموظف</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> البيانات الأساسية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">الرقم العسكري:</h6>
                            <p>{{ employee.military_id }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">الرقم الوطني:</h6>
                            <p>{{ employee.national_id or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">فصيلة الدم:</h6>
                            <p>{{ employee.blood_type or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">تاريخ الميلاد:</h6>
                            <p>{{ employee.date_of_birth.strftime('%Y-%m-%d') if employee.date_of_birth else 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">مكان الميلاد:</h6>
                            <p>{{ employee.birth_place or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">السكن الحالي:</h6>
                            <p>{{ employee.current_residence or 'غير محدد' }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-graduation-cap"></i> البيانات التعليمية والمهنية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">المؤهل العلمي:</h6>
                            <p>{{ employee.education or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">تاريخ الحصول على المؤهل:</h6>
                            <p>{{ employee.education_date.strftime('%Y-%m-%d') if employee.education_date else 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">الفئة:</h6>
                            <p>{{ employee.category.value if employee.category else 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">الرتبة:</h6>
                            <p>{{ employee.military_rank or 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">الوحدة:</h6>
                            <p>{{ employee.unit }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">العمل المكلف به:</h6>
                            <p>{{ employee.position }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">تاريخ التعيين:</h6>
                            <p>{{ employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else 'غير محدد' }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">تاريخ آخر ترقية:</h6>
                            <p>{{ employee.last_promotion_date.strftime('%Y-%m-%d') if employee.last_promotion_date else 'غير محدد' }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-money-check-alt"></i> البيانات المالية</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <h6 class="fw-bold">المصرف:</h6>
                                <p>{{ employee.bank_name or 'غير محدد' }}</p>
                            </div>
                            <div class="mb-3">
                                <h6 class="fw-bold">رقم الحساب المصرفي:</h6>
                                <p>{{ employee.bank_account or 'غير محدد' }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-phone-alt"></i> بيانات الاتصال</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <h6 class="fw-bold">رقم الهاتف:</h6>
                                <p>{{ employee.phone or 'غير محدد' }}</p>
                            </div>
                            <div class="mb-3">
                                <h6 class="fw-bold">البريد الإلكتروني:</h6>
                                <p>{{ employee.email or 'غير محدد' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-calendar-alt"></i> بيانات الإجازات</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">رصيد الإجازة:</h6>
                            <p>{{ employee.leave_balance }} يوم</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">الحالة:</h6>
                            <p>
                                <span class="badge
                                    {% if employee.status.name == 'ACTIVE' %}bg-success
                                    {% elif employee.status.name == 'ABSENT' %}bg-danger
                                    {% elif employee.status.name == 'ASSIGNED' %}bg-info
                                    {% elif employee.status.name == 'ON_LEAVE' %}bg-warning
                                    {% elif employee.status.name == 'SUSPENDED' %}bg-secondary
                                    {% elif employee.status.name == 'SCATTERED' %}bg-primary
                                    {% elif employee.status.name == 'MEDICAL' %}bg-dark
                                    {% endif %}">
                                    {{ employee.status.value }}
                                </span>
                            </p>
                        </div>
                        {% if employee.status_notes %}
                        <div class="col-md-12 mb-3">
                            <h6 class="fw-bold">ملاحظات الحالة:</h6>
                            <p>{{ employee.status_notes }}</p>
                        </div>
                        {% endif %}
                    </div>

                    <div class="d-grid">
                        <a href="{{ url_for('leaves.index', employee_id=employee.id) }}" class="btn btn-info">
                            <i class="fas fa-calendar-alt"></i> عرض سجل الإجازات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="d-flex justify-content-between mt-4">
        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
            <i class="fas fa-trash"></i> حذف الموظف
        </button>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    هل أنت متأكد من حذف الموظف <strong>{{ employee.name }}</strong>؟
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form action="{{ url_for('employees.delete', id=employee.id) }}" method="POST">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
