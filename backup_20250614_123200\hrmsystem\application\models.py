from . import db
from flask_login import UserMixin
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
import enum

class Permission(enum.Enum):
    # صلاحيات الموظفين
    VIEW_EMPLOYEES = 'عرض الموظفين'
    ADD_EMPLOYEE = 'إضافة موظف'
    EDIT_EMPLOYEE = 'تعديل بيانات موظف'
    DELETE_EMPLOYEE = 'حذف موظف'
    EXPORT_EMPLOYEES = 'تصدير بيانات الموظفين'
    IMPORT_EMPLOYEES = 'استيراد بيانات الموظفين'
    PRINT_EMPLOYEE = 'طباعة بيانات موظف'

    # صلاحيات الإجازات
    VIEW_LEAVES = 'عرض الإجازات'
    ADD_LEAVE = 'إضافة إجازة'
    EDIT_LEAVE = 'تعديل إجازة'
    DELETE_LEAVE = 'حذف إجازة'
    APPROVE_LEAVE = 'الموافقة على الإجازات'
    REJECT_LEAVE = 'رفض الإجازات'

    # صلاحيات المستخدمين
    VIEW_USERS = 'عرض المستخدمين'
    ADD_USER = 'إضافة مستخدم'
    EDIT_USER = 'تعديل بيانات مستخدم'
    DELETE_USER = 'حذف مستخدم'
    MANAGE_PERMISSIONS = 'إدارة الصلاحيات'

    # صلاحيات التقارير
    VIEW_REPORTS = 'عرض التقارير'
    EXPORT_REPORTS = 'تصدير التقارير'
    PRINT_REPORTS = 'طباعة التقارير'

    # صلاحيات النظام
    VIEW_AUDIT_LOGS = 'عرض سجلات التدقيق'
    MANAGE_SYSTEM = 'إدارة النظام'

class UserRole(enum.Enum):
    ADMIN = 'مدير النظام'
    SUPERVISOR = 'مشرف'
    USER = 'مستخدم عادي'

class UserStatus(enum.Enum):
    ACTIVE = 'نشط'
    INACTIVE = 'غير نشط'
    SUSPENDED = 'موقوف'

# جدول ربط المستخدمين بالصلاحيات
user_permissions = db.Table('user_permissions',
    db.Column('user_id', db.Integer, db.ForeignKey('user.id'), primary_key=True),
    db.Column('permission_name', db.String(50), primary_key=True)
)

class User(db.Model, UserMixin):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    email = db.Column(db.String(150), unique=True, nullable=False)
    password = db.Column(db.String(150), nullable=False)
    role = db.Column(db.Enum(UserRole), default=UserRole.USER)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    last_login = db.Column(db.DateTime, nullable=True)
    full_name = db.Column(db.Text, nullable=True)
    status = db.Column(db.Enum(UserStatus), default=UserStatus.ACTIVE)
    phone = db.Column(db.Text, nullable=True)
    profile_image = db.Column(db.Text, nullable=True)
    is_admin = db.Column(db.Boolean, default=False)

    # الخواص المحسوبة
    @property
    def is_active(self):
        return True  # دائماً نشط

    # لم نعد بحاجة إلى خاصية is_admin المحسوبة لأننا أضفنا عمود is_admin
    # @property
    # def is_admin(self):
    #     return self.role == UserRole.ADMIN

    @property
    def permissions(self):
        # الصلاحيات الافتراضية حسب الدور
        if self.is_admin:
            # مدير النظام لديه جميع الصلاحيات
            return [p.name for p in Permission]
        elif self.role == UserRole.SUPERVISOR:
            # المشرف لديه صلاحيات محددة
            return [
                'VIEW_EMPLOYEES', 'VIEW_LEAVES',
                'ADD_LEAVE', 'EDIT_LEAVE',
                'APPROVE_LEAVE', 'REJECT_LEAVE',
                'VIEW_REPORTS', 'PRINT_REPORTS',
                'EXPORT_REPORTS'
            ]
        else:
            # المستخدم العادي ليس لديه صلاحيات
            return []

    def set_password(self, password):
        self.password = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password, password)

    def has_permission(self, permission):
        """التحقق من وجود صلاحية محددة للمستخدم"""
        # مدير النظام لديه جميع الصلاحيات
        if self.is_admin or self.role == UserRole.ADMIN:
            return True

        # التحقق من الصلاحيات المخصصة للمستخدم
        return permission.name in self.permissions

    def add_permission(self, permission):
        """إضافة صلاحية للمستخدم - لا تأثير لها في النموذج الحالي"""
        pass

    def remove_permission(self, permission):
        """إزالة صلاحية من المستخدم - لا تأثير لها في النموذج الحالي"""
        pass

    def set_permissions(self, permissions):
        """تعيين مجموعة من الصلاحيات للمستخدم - لا تأثير لها في النموذج الحالي"""
        pass

    def __repr__(self):
        return f'<User {self.username}>'

class EmployeeCategory(enum.Enum):
    OFFICER = 'ضباط'
    NCO = 'ضباط صف'
    EMPLOYEE = 'موظف'

class EmployeeStatus(enum.Enum):
    ACTIVE = 'مستمر'
    ABSENT = 'غائب/هارب'
    ASSIGNED = 'منتدب'
    ON_LEAVE = 'في إجازة'
    SUSPENDED = 'موقوف'
    SCATTERED = 'متفرق'
    MEDICAL = 'عيادة طبية'

class Department(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    employees = db.relationship('Employee', backref='department_rel', lazy=True)

    def __repr__(self):
        return f'<Department {self.name}>'


class Employee(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    military_id = db.Column(db.String(20), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    national_id = db.Column(db.String(20), unique=True, nullable=True)
    blood_type = db.Column(db.String(10), nullable=True)
    date_of_birth = db.Column(db.Date, nullable=True)
    birth_place = db.Column(db.String(100), nullable=True)
    current_residence = db.Column(db.String(200), nullable=True)
    education = db.Column(db.String(100), nullable=True)
    education_date = db.Column(db.Date, nullable=True)
    category = db.Column(db.Enum(EmployeeCategory), nullable=True)
    bank_name = db.Column(db.String(100), nullable=True)
    bank_account = db.Column(db.String(50), nullable=True)
    military_rank = db.Column(db.String(50), nullable=True)
    unit = db.Column(db.String(100), nullable=True)
    position = db.Column(db.String(100), nullable=True)
    status = db.Column(db.Enum(EmployeeStatus), default=EmployeeStatus.ACTIVE)
    status_notes = db.Column(db.Text, nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    email = db.Column(db.String(120), nullable=True)
    profile_image = db.Column(db.String(255), nullable=True)
    leave_balance = db.Column(db.Integer, default=0)
    hire_date = db.Column(db.Date, nullable=True)
    last_promotion_date = db.Column(db.Date, nullable=True)
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    leave_requests = db.relationship('LeaveRequest', backref='employee', lazy=True)

    def __repr__(self):
        return f'<Employee {self.name}>'

class LeaveType(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text, nullable=True)
    color = db.Column(db.String(20), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    leave_requests = db.relationship('LeaveRequest', backref='leave_type_rel', lazy=True)

    def __repr__(self):
        return f'<LeaveType {self.name}>'

class LeaveStatus(enum.Enum):
    PENDING = 'قيد الانتظار'
    APPROVED = 'موافق عليها'
    REJECTED = 'مرفوضة'
    CANCELLED = 'ملغية'

class LeaveRequest(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    leave_type_id = db.Column(db.Integer, db.ForeignKey('leave_type.id'), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    total_days = db.Column(db.Integer, nullable=False)
    reason = db.Column(db.Text, nullable=True)
    status = db.Column(db.Enum(LeaveStatus), default=LeaveStatus.PENDING)
    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    approved_at = db.Column(db.DateTime, nullable=True)
    rejection_reason = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    approver = db.relationship('User', backref='approved_leaves', foreign_keys=[approved_by])

    def __repr__(self):
        return f'<LeaveRequest {self.id}>'

class Holiday(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    date = db.Column(db.Date, nullable=False)
    description = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def __repr__(self):
        return f'<Holiday {self.name}>'

class AuditLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    action = db.Column(db.String(50), nullable=False)
    entity = db.Column(db.String(50), nullable=False)
    entity_id = db.Column(db.Integer, nullable=True)
    details = db.Column(db.Text, nullable=True)
    ip_address = db.Column(db.String(50), nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.now)

    # Relationships
    user = db.relationship('User', backref='audit_logs')

    def __init__(self, **kwargs):
        # إذا لم يتم تحديد user_id، استخدم المستخدم الحالي إذا كان متاحًا
        if 'user_id' not in kwargs:
            try:
                from flask_login import current_user
                if hasattr(current_user, 'id') and current_user.is_authenticated:
                    kwargs['user_id'] = current_user.id
                else:
                    # إذا لم يكن هناك مستخدم حالي، استخدم المستخدم الأول في النظام
                    first_user = User.query.first()
                    if first_user:
                        kwargs['user_id'] = first_user.id
                    else:
                        # إذا لم يكن هناك مستخدمين في النظام، استخدم قيمة افتراضية
                        kwargs['user_id'] = 1
            except Exception:
                # في حالة حدوث أي خطأ، استخدم قيمة افتراضية
                kwargs['user_id'] = 1

        super(AuditLog, self).__init__(**kwargs)

    def __repr__(self):
        return f'<AuditLog {self.id}>'

class Notification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(100), nullable=False)
    message = db.Column(db.Text, nullable=False)
    is_read = db.Column(db.Boolean, default=False)
    link = db.Column(db.String(255), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.now)

    # Relationships
    user = db.relationship('User', backref='notifications')

    def __repr__(self):
        return f'<Notification {self.id}>'

class UserAuditLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    action_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    action = db.Column(db.String(50), nullable=False)  # create, update, delete, login, logout, password_change, etc.
    old_values = db.Column(db.Text, nullable=True)  # JSON string of old values
    new_values = db.Column(db.Text, nullable=True)  # JSON string of new values
    ip_address = db.Column(db.String(50), nullable=True)
    user_agent = db.Column(db.String(255), nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.now)

    # Relationships
    user = db.relationship('User', foreign_keys=[user_id], backref='audit_logs_received')
    actor = db.relationship('User', foreign_keys=[action_by], backref='audit_logs_performed')

    def __init__(self, **kwargs):
        # إذا لم يتم تحديد user_id، استخدم المستخدم المحدد في action_by
        if 'user_id' not in kwargs and 'action_by' in kwargs:
            kwargs['user_id'] = kwargs['action_by']
        # إذا لم يتم تحديد user_id ولا action_by، استخدم المستخدم الحالي إذا كان متاحًا
        if 'user_id' not in kwargs:
            try:
                from flask_login import current_user
                if hasattr(current_user, 'id') and current_user.is_authenticated:
                    kwargs['user_id'] = current_user.id
                    # إذا لم يتم تحديد action_by، استخدم المستخدم الحالي أيضًا
                    if 'action_by' not in kwargs:
                        kwargs['action_by'] = current_user.id
                else:
                    # إذا لم يكن هناك مستخدم حالي، استخدم المستخدم الأول في النظام
                    first_user = User.query.first()
                    if first_user:
                        kwargs['user_id'] = first_user.id
                        # إذا لم يتم تحديد action_by، استخدم المستخدم الأول أيضًا
                        if 'action_by' not in kwargs:
                            kwargs['action_by'] = first_user.id
                    else:
                        # إذا لم يكن هناك مستخدمين في النظام، استخدم قيمة افتراضية
                        kwargs['user_id'] = 1
                        # إذا لم يتم تحديد action_by، استخدم قيمة افتراضية أيضًا
                        if 'action_by' not in kwargs:
                            kwargs['action_by'] = 1
            except Exception:
                # في حالة حدوث أي خطأ، استخدم قيمة افتراضية
                kwargs['user_id'] = 1
                # إذا لم يتم تحديد action_by، استخدم قيمة افتراضية أيضًا
                if 'action_by' not in kwargs:
                    kwargs['action_by'] = 1

        # إذا لم يتم تحديد action_by، استخدم user_id
        if 'action_by' not in kwargs and 'user_id' in kwargs:
            kwargs['action_by'] = kwargs['user_id']

        super(UserAuditLog, self).__init__(**kwargs)

    def __repr__(self):
        return f'<UserAuditLog {self.id}>'
