name: HRM System Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, '3.10', '3.11']

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r hrmsystem/requirements.txt
        pip install pytest pytest-flask pytest-cov Flask-Limiter bleach

    - name: Set up environment variables
      run: |
        echo "FLASK_ENV=testing" >> $GITHUB_ENV
        echo "SECRET_KEY=test-secret-key-for-ci" >> $GITHUB_ENV

    - name: Run linting
      run: |
        pip install flake8
        # Stop the build if there are Python syntax errors or undefined names
        flake8 hrmsystem --count --select=E9,F63,F7,F82 --show-source --statistics
        # Exit-zero treats all errors as warnings
        flake8 hrmsystem --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

    - name: Run security checks
      run: |
        pip install bandit safety
        # Run bandit security linter
        bandit -r hrmsystem -f json -o bandit-report.json || true
        # Check for known security vulnerabilities
        safety check --json --output safety-report.json || true

    - name: Run unit tests
      run: |
        python -m pytest tests/ -v --tb=short

    - name: Run tests with coverage
      run: |
        python -m pytest tests/ --cov=hrmsystem --cov-report=xml --cov-report=html --cov-report=term

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

    - name: Archive test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.python-version }}
        path: |
          htmlcov/
          bandit-report.json
          safety-report.json
          coverage.xml

    - name: Archive logs
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: test-logs-${{ matrix.python-version }}
        path: |
          logs/
          *.log

  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  integration-tests:
    runs-on: ubuntu-latest
    needs: test
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test_password
          MYSQL_DATABASE: hrm_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r hrmsystem/requirements.txt
        pip install pytest pytest-flask pytest-cov Flask-Limiter bleach

    - name: Wait for MySQL
      run: |
        while ! mysqladmin ping -h"127.0.0.1" -P3306 -uroot -ptest_password --silent; do
          sleep 1
        done

    - name: Set up test database
      run: |
        mysql -h127.0.0.1 -P3306 -uroot -ptest_password -e "CREATE DATABASE IF NOT EXISTS hrm_test;"

    - name: Run integration tests
      env:
        DATABASE_URL: mysql+pymysql://root:test_password@127.0.0.1:3306/hrm_test
        FLASK_ENV: testing
      run: |
        python -m pytest tests/ -m integration -v

  performance-tests:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r hrmsystem/requirements.txt
        pip install locust

    - name: Run performance tests
      run: |
        # Add performance tests here when available
        echo "Performance tests would run here"

  deploy-staging:
    runs-on: ubuntu-latest
    needs: [test, security-scan, integration-tests]
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to staging
      run: |
        echo "Deploy to staging environment"
        # Add deployment steps here

  deploy-production:
    runs-on: ubuntu-latest
    needs: [test, security-scan, integration-tests]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to production
      run: |
        echo "Deploy to production environment"
        # Add deployment steps here
