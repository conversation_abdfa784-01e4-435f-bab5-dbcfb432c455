{% extends 'layouts/base.html' %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-user-edit"></i> تعديل بيانات الموظف</h2>
        <a href="{{ url_for('employees.view', id=employee.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة إلى بيانات الموظف
        </a>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-user-edit"></i> بيانات الموظف</h5>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ url_for('employees.edit', id=employee.id) }}" enctype="multipart/form-data">
                {{ form.hidden_tag() }}

                <div class="row">
                    <!-- البيانات الأساسية -->
                    <div class="col-md-12 mb-4">
                        <h4 class="border-bottom pb-2 mb-3">البيانات الأساسية</h4>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                {{ form.military_id.label(class="form-label") }}
                                {% if form.military_id.errors %}
                                    {{ form.military_id(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.military_id.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.military_id(class="form-control") }}
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                {{ form.name.label(class="form-label") }}
                                {% if form.name.errors %}
                                    {{ form.name(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.name(class="form-control") }}
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                {{ form.national_id.label(class="form-label") }}
                                {% if form.national_id.errors %}
                                    {{ form.national_id(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.national_id.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.national_id(class="form-control") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- البيانات الشخصية -->
                    <div class="col-md-12 mb-4">
                        <h4 class="border-bottom pb-2 mb-3">البيانات الشخصية</h4>
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                {{ form.blood_type.label(class="form-label") }}
                                {% if form.blood_type.errors %}
                                    {{ form.blood_type(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.blood_type.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.blood_type(class="form-select") }}
                                {% endif %}
                            </div>

                            <div class="col-md-3 mb-3">
                                {{ form.date_of_birth.label(class="form-label") }}
                                {% if form.date_of_birth.errors %}
                                    {{ form.date_of_birth(class="form-control is-invalid", type="date") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.date_of_birth.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.date_of_birth(class="form-control", type="date") }}
                                {% endif %}
                            </div>

                            <div class="col-md-3 mb-3">
                                {{ form.birth_place.label(class="form-label") }}
                                {% if form.birth_place.errors %}
                                    {{ form.birth_place(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.birth_place.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.birth_place(class="form-control") }}
                                {% endif %}
                            </div>

                            <div class="col-md-3 mb-3">
                                {{ form.current_residence.label(class="form-label") }}
                                {% if form.current_residence.errors %}
                                    {{ form.current_residence(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.current_residence.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.current_residence(class="form-control") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- البيانات التعليمية والمهنية -->
                    <div class="col-md-12 mb-4">
                        <h4 class="border-bottom pb-2 mb-3">البيانات التعليمية والمهنية</h4>
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                {{ form.education.label(class="form-label") }}
                                {% if form.education.errors %}
                                    {{ form.education(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.education.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.education(class="form-control") }}
                                {% endif %}
                            </div>

                            <div class="col-md-3 mb-3">
                                {{ form.education_date.label(class="form-label") }}
                                {% if form.education_date.errors %}
                                    {{ form.education_date(class="form-control is-invalid", type="date") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.education_date.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.education_date(class="form-control", type="date") }}
                                {% endif %}
                            </div>

                            <div class="col-md-3 mb-3">
                                {{ form.category.label(class="form-label") }}
                                {% if form.category.errors %}
                                    {{ form.category(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.category.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.category(class="form-select") }}
                                {% endif %}
                            </div>

                            <div class="col-md-3 mb-3">
                                {{ form.military_rank.label(class="form-label") }}
                                {% if form.military_rank.errors %}
                                    {{ form.military_rank(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.military_rank.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.military_rank(class="form-control") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- البيانات الوظيفية -->
                    <div class="col-md-12 mb-4">
                        <h4 class="border-bottom pb-2 mb-3">البيانات الوظيفية</h4>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                {{ form.unit.label(class="form-label") }}
                                {% if form.unit.errors %}
                                    {{ form.unit(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.unit.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.unit(class="form-control") }}
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                {{ form.position.label(class="form-label") }}
                                {% if form.position.errors %}
                                    {{ form.position(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.position.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.position(class="form-control") }}
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                {{ form.status.label(class="form-label") }}
                                {% if form.status.errors %}
                                    {{ form.status(class="form-select is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.status.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.status(class="form-select") }}
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                {{ form.hire_date.label(class="form-label") }}
                                {% if form.hire_date.errors %}
                                    {{ form.hire_date(class="form-control is-invalid", type="date") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.hire_date.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.hire_date(class="form-control", type="date") }}
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                {{ form.last_promotion_date.label(class="form-label") }}
                                {% if form.last_promotion_date.errors %}
                                    {{ form.last_promotion_date(class="form-control is-invalid", type="date") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.last_promotion_date.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.last_promotion_date(class="form-control", type="date") }}
                                {% endif %}
                            </div>

                            <div class="col-md-12 mb-3">
                                {{ form.status_notes.label(class="form-label") }}
                                {% if form.status_notes.errors %}
                                    {{ form.status_notes(class="form-control is-invalid", rows=3) }}
                                    <div class="invalid-feedback">
                                        {% for error in form.status_notes.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.status_notes(class="form-control", rows=3) }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- البيانات المالية -->
                    <div class="col-md-12 mb-4">
                        <h4 class="border-bottom pb-2 mb-3">البيانات المالية</h4>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                {{ form.bank_name.label(class="form-label") }}
                                {% if form.bank_name.errors %}
                                    {{ form.bank_name(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.bank_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.bank_name(class="form-control") }}
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                {{ form.bank_account.label(class="form-label") }}
                                {% if form.bank_account.errors %}
                                    {{ form.bank_account(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.bank_account.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.bank_account(class="form-control") }}
                                {% endif %}
                            </div>

                            <div class="col-md-4 mb-3">
                                {{ form.leave_balance.label(class="form-label") }}
                                {% if form.leave_balance.errors %}
                                    {{ form.leave_balance(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.leave_balance.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.leave_balance(class="form-control") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- بيانات الاتصال -->
                    <div class="col-md-12 mb-4">
                        <h4 class="border-bottom pb-2 mb-3">بيانات الاتصال</h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.phone.label(class="form-label") }}
                                {% if form.phone.errors %}
                                    {{ form.phone(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.phone.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.phone(class="form-control") }}
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                {{ form.email.label(class="form-label") }}
                                {% if form.email.errors %}
                                    {{ form.email(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.email.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.email(class="form-control") }}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- الصورة الشخصية -->
                    <div class="col-md-12 mb-4">
                        <h4 class="border-bottom pb-2 mb-3">الصورة الشخصية</h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {% if employee.profile_image %}
                                <div class="mb-3">
                                    <p>الصورة الحالية:</p>
                                    <img src="{{ url_for('static', filename='img/employees/' + employee.profile_image) }}" alt="{{ employee.name }}" class="img-thumbnail" style="max-height: 150px;">
                                </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                {{ form.profile_image.label(class="form-label") }}
                                {% if form.profile_image.errors %}
                                    {{ form.profile_image(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.profile_image.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.profile_image(class="form-control") }}
                                {% endif %}
                                <div class="form-text">يسمح فقط بملفات الصور (jpg, jpeg, png). اترك هذا الحقل فارغًا للاحتفاظ بالصورة الحالية.</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{{ url_for('employees.view', id=employee.id) }}" class="btn btn-secondary me-md-2">إلغاء</a>
                    {{ form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
