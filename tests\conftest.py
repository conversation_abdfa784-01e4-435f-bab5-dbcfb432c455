"""
Test configuration and fixtures for HRM System
"""
import pytest
import tempfile
import os
from hrmsystem.application import create_app, db
from hrmsystem.application.models import User, Employee, LeaveRequest, UserRole, EmployeeStatus, LeaveStatus


@pytest.fixture
def app():
    """Create and configure a new app instance for each test."""
    # Create a temporary file to isolate the database for each test
    db_fd, db_path = tempfile.mkstemp()
    
    # Create app with test configuration
    app = create_app()
    app.config.update({
        'TESTING': True,
        'SQLALCHEMY_DATABASE_URI': f'sqlite:///{db_path}',
        'WTF_CSRF_ENABLED': False,  # Disable CSRF for testing
        'SECRET_KEY': 'test-secret-key'
    })
    
    with app.app_context():
        db.create_all()
        yield app
        
    # Clean up
    os.close(db_fd)
    os.unlink(db_path)


@pytest.fixture
def client(app):
    """A test client for the app."""
    return app.test_client()


@pytest.fixture
def runner(app):
    """A test runner for the app's Click commands."""
    return app.test_cli_runner()


@pytest.fixture
def admin_user(app):
    """Create an admin user for testing."""
    with app.app_context():
        user = User(
            username='admin_test',
            email='<EMAIL>',
            role=UserRole.ADMIN,
            is_admin=True,
            full_name='مدير الاختبار'
        )
        user.set_password('admin123')
        db.session.add(user)
        db.session.commit()
        return user


@pytest.fixture
def regular_user(app):
    """Create a regular user for testing."""
    with app.app_context():
        user = User(
            username='user_test',
            email='<EMAIL>',
            role=UserRole.USER,
            full_name='مستخدم الاختبار'
        )
        user.set_password('user123')
        db.session.add(user)
        db.session.commit()
        return user


@pytest.fixture
def sample_employee(app):
    """Create a sample employee for testing."""
    with app.app_context():
        employee = Employee(
            military_id='12345',
            name='أحمد محمد علي',
            national_id='1234567890123',
            blood_type='O+',
            military_rank='ملازم',
            unit='الوحدة الأولى',
            position='ضابط',
            status=EmployeeStatus.ACTIVE,
            phone='0123456789',
            email='<EMAIL>'
        )
        db.session.add(employee)
        db.session.commit()
        return employee


@pytest.fixture
def authenticated_client(client, admin_user):
    """A client with an authenticated admin user."""
    with client.session_transaction() as sess:
        sess['_user_id'] = str(admin_user.id)
        sess['_fresh'] = True
    return client


class AuthActions:
    """Helper class for authentication actions in tests."""
    
    def __init__(self, client):
        self._client = client
    
    def login(self, username='admin_test', password='admin123'):
        """Log in a user."""
        return self._client.post(
            '/auth/login',
            data={'username': username, 'password': password},
            follow_redirects=True
        )
    
    def logout(self):
        """Log out the current user."""
        return self._client.get('/auth/logout', follow_redirects=True)


@pytest.fixture
def auth(client):
    """Authentication helper."""
    return AuthActions(client)
