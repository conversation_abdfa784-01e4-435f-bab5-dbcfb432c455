from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from ..models import User, AuditLog
from .. import db
from .forms import <PERSON>ginForm, RegistrationForm, ChangePasswordForm

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and check_password_hash(user.password, form.password.data):
            # تحديث آخر تسجيل دخول
            user.last_login = db.func.current_timestamp()
            db.session.commit()

            # تسجيل دخول المستخدم
            login_user(user, remember=form.remember_me.data)

            try:
                # إنشاء سجل تدقيق
                log = AuditLog(
                    user_id=user.id,
                    action='login',
                    entity='User',
                    entity_id=user.id,
                    details=f'تم تسجيل الدخول بنجاح',
                    ip_address=request.remote_addr
                )
                db.session.add(log)
                db.session.commit()
            except Exception as e:
                # تجاهل أي أخطاء في سجل التدقيق
                print(f"Error creating audit log: {e}")
                db.session.rollback()

            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('dashboard.index'))
        else:
            flash('فشل تسجيل الدخول. يرجى التحقق من اسم المستخدم وكلمة المرور.', 'danger')

    return render_template('auth/login.html', form=form, title='تسجيل الدخول')

@auth_bp.route('/logout')
@login_required
def logout():
    # Create audit log
    log = AuditLog(
        user_id=current_user.id,
        action='logout',
        entity='User',
        entity_id=current_user.id,
        details=f'تم تسجيل الخروج',
        ip_address=request.remote_addr
    )
    db.session.add(log)
    db.session.commit()

    logout_user()
    flash('تم تسجيل الخروج بنجاح.', 'success')
    return redirect(url_for('auth.login'))

@auth_bp.route('/register', methods=['GET', 'POST'])
@login_required
def register():
    if not current_user.is_admin:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة.', 'danger')
        return redirect(url_for('dashboard.index'))

    form = RegistrationForm()
    if form.validate_on_submit():
        hashed_password = generate_password_hash(form.password.data)
        user = User(
            username=form.username.data,
            email=form.email.data,
            password=hashed_password,
            is_admin=form.is_admin.data
        )
        db.session.add(user)

        # Create audit log
        log = AuditLog(
            user_id=current_user.id,
            action='create',
            entity='User',
            entity_id=user.id,
            details=f'تم إنشاء المستخدم {user.username}',
            ip_address=request.remote_addr
        )
        db.session.add(log)

        db.session.commit()
        flash(f'تم إنشاء الحساب بنجاح للمستخدم {form.username.data}!', 'success')
        return redirect(url_for('auth.users'))

    return render_template('auth/register.html', form=form, title='تسجيل مستخدم جديد')

@auth_bp.route('/users')
@login_required
def users():
    if not current_user.is_admin:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة.', 'danger')
        return redirect(url_for('dashboard.index'))

    users = User.query.all()
    return render_template('auth/users.html', users=users, title='إدارة المستخدمين')

@auth_bp.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    form = ChangePasswordForm()
    if form.validate_on_submit():
        if check_password_hash(current_user.password, form.current_password.data):
            current_user.password = generate_password_hash(form.new_password.data)

            # Create audit log
            log = AuditLog(
                user_id=current_user.id,
                action='update',
                entity='User',
                entity_id=current_user.id,
                details=f'تم تغيير كلمة المرور',
                ip_address=request.remote_addr
            )
            db.session.add(log)

            db.session.commit()
            flash('تم تغيير كلمة المرور بنجاح!', 'success')
            return redirect(url_for('dashboard.index'))
        else:
            flash('كلمة المرور الحالية غير صحيحة.', 'danger')

    return render_template('auth/change_password.html', form=form, title='تغيير كلمة المرور')

@auth_bp.route('/delete_user/<int:id>', methods=['POST'])
@login_required
def delete_user(id):
    if not current_user.is_admin:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة.', 'danger')
        return redirect(url_for('dashboard.index'))

    user = User.query.get_or_404(id)

    # Don't allow deleting your own account
    if user.id == current_user.id:
        flash('لا يمكنك حذف حسابك الحالي.', 'danger')
        return redirect(url_for('auth.users'))

    # Create audit log
    log = AuditLog(
        user_id=current_user.id,
        action='delete',
        entity='User',
        entity_id=user.id,
        details=f'تم حذف المستخدم {user.username}',
        ip_address=request.remote_addr
    )
    db.session.add(log)

    db.session.delete(user)
    db.session.commit()
    flash(f'تم حذف المستخدم {user.username} بنجاح!', 'success')
    return redirect(url_for('auth.users'))
