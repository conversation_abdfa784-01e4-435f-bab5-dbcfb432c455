["tests/test_api.py::TestAPIAuth::test_api_login_invalid_credentials", "tests/test_api.py::TestAPIAuth::test_api_login_missing_data", "tests/test_api.py::TestAPIAuth::test_api_login_valid_credentials", "tests/test_api.py::TestAPIEmployees::test_create_employee_duplicate_military_id", "tests/test_api.py::TestAPIEmployees::test_create_employee_missing_data", "tests/test_api.py::TestAPIEmployees::test_create_employee_valid_data", "tests/test_api.py::TestAPIEmployees::test_get_employee_by_id", "tests/test_api.py::TestAPIEmployees::test_get_employees_with_filters", "tests/test_api.py::TestAPIEmployees::test_get_employees_with_search", "tests/test_api.py::TestAPIEmployees::test_get_employees_with_token", "tests/test_api.py::TestAPIEmployees::test_get_employees_without_token", "tests/test_api.py::TestAPIEmployees::test_get_nonexistent_employee", "tests/test_api.py::TestAPILeaves::test_approve_leave_request", "tests/test_api.py::TestAPILeaves::test_get_leaves_with_token", "tests/test_api.py::TestAPILeaves::test_get_leaves_without_token", "tests/test_api.py::TestAPILeaves::test_reject_leave_request", "tests/test_api.py::TestAPIPermissions::test_invalid_token", "tests/test_api.py::TestAPIPermissions::test_regular_user_cannot_access_admin_endpoints", "tests/test_api.py::TestAPIStats::test_dashboard_stats", "tests/test_auth.py::TestAuth::test_invalid_login_wrong_password", "tests/test_auth.py::TestAuth::test_invalid_login_wrong_username", "tests/test_auth.py::TestAuth::test_login_page_loads", "tests/test_auth.py::TestAuth::test_login_required_redirect", "tests/test_auth.py::TestAuth::test_logout", "tests/test_auth.py::TestAuth::test_password_hashing", "tests/test_auth.py::TestAuth::test_valid_login", "tests/test_auth.py::TestUserPermissions::test_admin_permissions", "tests/test_auth.py::TestUserPermissions::test_regular_user_permissions", "tests/test_auth.py::TestUserPermissions::test_supervisor_permissions", "tests/test_auth.py::TestUserRegistration::test_duplicate_email_registration", "tests/test_auth.py::TestUserRegistration::test_duplicate_username_registration", "tests/test_auth.py::TestUserRegistration::test_password_mismatch_registration", "tests/test_auth.py::TestUserRegistration::test_register_page_loads", "tests/test_auth.py::TestUserRegistration::test_valid_registration", "tests/test_employees.py::TestEmployeeCreation::test_create_employee_duplicate_military_id", "tests/test_employees.py::TestEmployeeCreation::test_create_employee_missing_required_fields", "tests/test_employees.py::TestEmployeeCreation::test_create_employee_valid_data", "tests/test_employees.py::TestEmployeeDelete::test_delete_employee", "tests/test_employees.py::TestEmployeeDelete::test_delete_nonexistent_employee", "tests/test_employees.py::TestEmployeeFilters::test_filter_by_status", "tests/test_employees.py::TestEmployeeFilters::test_filter_by_unit", "tests/test_employees.py::TestEmployeeSearch::test_search_by_military_id", "tests/test_employees.py::TestEmployeeSearch::test_search_by_name", "tests/test_employees.py::TestEmployeeSearch::test_search_no_results", "tests/test_employees.py::TestEmployeeUpdate::test_update_employee_valid_data", "tests/test_employees.py::TestEmployeeUpdate::test_update_nonexistent_employee", "tests/test_employees.py::TestEmployeeViews::test_employee_create_form_loads", "tests/test_employees.py::TestEmployeeViews::test_employee_detail_view", "tests/test_employees.py::TestEmployeeViews::test_employee_edit_form_loads", "tests/test_employees.py::TestEmployeeViews::test_employees_list_loads", "tests/test_employees.py::TestEmployeeViews::test_employees_list_requires_login", "tests/test_leaves.py::TestLeaveApproval::test_approve_leave_request", "tests/test_leaves.py::TestLeaveApproval::test_reject_leave_request", "tests/test_leaves.py::TestLeaveCalendar::test_leave_calendar_api", "tests/test_leaves.py::TestLeaveCalendar::test_leave_calendar_loads", "tests/test_leaves.py::TestLeaveCreation::test_create_leave_invalid_dates", "tests/test_leaves.py::TestLeaveCreation::test_create_leave_past_dates", "tests/test_leaves.py::TestLeaveCreation::test_create_leave_valid_data", "tests/test_leaves.py::TestLeaveReports::test_leave_report_loads", "tests/test_leaves.py::TestLeaveReports::test_leave_report_with_filters", "tests/test_leaves.py::TestLeaveTypes::test_create_leave_type", "tests/test_leaves.py::TestLeaveTypes::test_leave_types_list", "tests/test_leaves.py::TestLeaveViews::test_leave_create_form_loads", "tests/test_leaves.py::TestLeaveViews::test_leaves_list_loads", "tests/test_leaves.py::TestLeaveViews::test_leaves_list_requires_login"]