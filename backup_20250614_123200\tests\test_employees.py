"""
Employee management tests for HRM System
"""
import pytest
from hrmsystem.application.models import Employee, EmployeeStatus, EmployeeCategory


class TestEmployeeViews:
    """Test employee view functionality."""
    
    def test_employees_list_requires_login(self, client):
        """Test that employees list requires authentication."""
        response = client.get('/employees/')
        assert response.status_code == 302
        assert '/auth/login' in response.location
    
    def test_employees_list_loads(self, authenticated_client, sample_employee):
        """Test that employees list loads for authenticated user."""
        response = authenticated_client.get('/employees/')
        assert response.status_code == 200
        assert 'قائمة الموظفين' in response.get_data(as_text=True)
        assert sample_employee.name in response.get_data(as_text=True)
    
    def test_employee_detail_view(self, authenticated_client, sample_employee):
        """Test employee detail view."""
        response = authenticated_client.get(f'/employees/view/{sample_employee.id}')
        assert response.status_code == 200
        assert sample_employee.name in response.get_data(as_text=True)
        assert sample_employee.military_id in response.get_data(as_text=True)
    
    def test_employee_create_form_loads(self, authenticated_client):
        """Test that employee creation form loads."""
        response = authenticated_client.get('/employees/create')
        assert response.status_code == 200
        assert 'إضافة موظف جديد' in response.get_data(as_text=True)
    
    def test_employee_edit_form_loads(self, authenticated_client, sample_employee):
        """Test that employee edit form loads."""
        response = authenticated_client.get(f'/employees/edit/{sample_employee.id}')
        assert response.status_code == 200
        assert 'تعديل بيانات الموظف' in response.get_data(as_text=True)
        assert sample_employee.name in response.get_data(as_text=True)


class TestEmployeeCreation:
    """Test employee creation functionality."""
    
    def test_create_employee_valid_data(self, authenticated_client, app):
        """Test creating employee with valid data."""
        response = authenticated_client.post('/employees/create', data={
            'military_id': '67890',
            'name': 'محمد أحمد سالم',
            'national_id': '9876543210987',
            'blood_type': 'A+',
            'military_rank': 'نقيب',
            'unit': 'الوحدة الثانية',
            'position': 'ضابط عمليات',
            'phone': '0987654321',
            'email': '<EMAIL>'
        }, follow_redirects=True)
        
        assert response.status_code == 200
        assert 'تم إضافة الموظف بنجاح' in response.get_data(as_text=True)
        
        # Verify employee was created in database
        with app.app_context():
            employee = Employee.query.filter_by(military_id='67890').first()
            assert employee is not None
            assert employee.name == 'محمد أحمد سالم'
    
    def test_create_employee_duplicate_military_id(self, authenticated_client, sample_employee):
        """Test creating employee with duplicate military ID."""
        response = authenticated_client.post('/employees/create', data={
            'military_id': sample_employee.military_id,  # Duplicate
            'name': 'موظف آخر',
            'national_id': '1111111111111',
            'military_rank': 'ملازم أول',
            'unit': 'وحدة أخرى'
        })
        
        assert response.status_code == 200
        assert 'الرقم العسكري موجود بالفعل' in response.get_data(as_text=True)
    
    def test_create_employee_missing_required_fields(self, authenticated_client):
        """Test creating employee with missing required fields."""
        response = authenticated_client.post('/employees/create', data={
            'name': 'موظف ناقص البيانات'
            # Missing military_id
        })
        
        assert response.status_code == 200
        assert 'هذا الحقل مطلوب' in response.get_data(as_text=True)


class TestEmployeeUpdate:
    """Test employee update functionality."""
    
    def test_update_employee_valid_data(self, authenticated_client, sample_employee, app):
        """Test updating employee with valid data."""
        new_name = 'أحمد محمد علي المحدث'
        response = authenticated_client.post(f'/employees/edit/{sample_employee.id}', data={
            'military_id': sample_employee.military_id,
            'name': new_name,
            'national_id': sample_employee.national_id,
            'blood_type': sample_employee.blood_type,
            'military_rank': sample_employee.military_rank,
            'unit': sample_employee.unit,
            'position': sample_employee.position,
            'phone': sample_employee.phone,
            'email': sample_employee.email
        }, follow_redirects=True)
        
        assert response.status_code == 200
        assert 'تم تحديث بيانات الموظف بنجاح' in response.get_data(as_text=True)
        
        # Verify employee was updated in database
        with app.app_context():
            updated_employee = Employee.query.get(sample_employee.id)
            assert updated_employee.name == new_name
    
    def test_update_nonexistent_employee(self, authenticated_client):
        """Test updating non-existent employee."""
        response = authenticated_client.get('/employees/edit/99999')
        assert response.status_code == 404


class TestEmployeeDelete:
    """Test employee deletion functionality."""
    
    def test_delete_employee(self, authenticated_client, sample_employee, app):
        """Test deleting an employee."""
        employee_id = sample_employee.id
        response = authenticated_client.post(f'/employees/delete/{employee_id}', 
                                           follow_redirects=True)
        
        assert response.status_code == 200
        assert 'تم حذف الموظف بنجاح' in response.get_data(as_text=True)
        
        # Verify employee was deleted from database
        with app.app_context():
            deleted_employee = Employee.query.get(employee_id)
            assert deleted_employee is None
    
    def test_delete_nonexistent_employee(self, authenticated_client):
        """Test deleting non-existent employee."""
        response = authenticated_client.post('/employees/delete/99999')
        assert response.status_code == 404


class TestEmployeeSearch:
    """Test employee search functionality."""
    
    def test_search_by_name(self, authenticated_client, sample_employee):
        """Test searching employees by name."""
        response = authenticated_client.get('/employees/', query_string={
            'search': 'أحمد'
        })
        
        assert response.status_code == 200
        assert sample_employee.name in response.get_data(as_text=True)
    
    def test_search_by_military_id(self, authenticated_client, sample_employee):
        """Test searching employees by military ID."""
        response = authenticated_client.get('/employees/', query_string={
            'search': sample_employee.military_id
        })
        
        assert response.status_code == 200
        assert sample_employee.name in response.get_data(as_text=True)
    
    def test_search_no_results(self, authenticated_client):
        """Test search with no results."""
        response = authenticated_client.get('/employees/', query_string={
            'search': 'موظف غير موجود'
        })
        
        assert response.status_code == 200
        assert 'لا توجد نتائج' in response.get_data(as_text=True)


class TestEmployeeFilters:
    """Test employee filtering functionality."""
    
    def test_filter_by_status(self, authenticated_client, app):
        """Test filtering employees by status."""
        with app.app_context():
            # Create employees with different statuses
            active_employee = Employee(
                military_id='11111',
                name='موظف نشط',
                status=EmployeeStatus.ACTIVE
            )
            inactive_employee = Employee(
                military_id='22222',
                name='موظف غير نشط',
                status=EmployeeStatus.SUSPENDED
            )
            
            from hrmsystem.application import db
            db.session.add_all([active_employee, inactive_employee])
            db.session.commit()
        
        # Filter by active status
        response = authenticated_client.get('/employees/', query_string={
            'status': 'ACTIVE'
        })
        
        assert response.status_code == 200
        assert 'موظف نشط' in response.get_data(as_text=True)
        assert 'موظف غير نشط' not in response.get_data(as_text=True)
    
    def test_filter_by_unit(self, authenticated_client, sample_employee):
        """Test filtering employees by unit."""
        response = authenticated_client.get('/employees/', query_string={
            'unit': sample_employee.unit
        })
        
        assert response.status_code == 200
        assert sample_employee.name in response.get_data(as_text=True)
