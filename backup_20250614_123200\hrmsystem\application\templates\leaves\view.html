{% extends 'layouts/base.html' %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-calendar-alt"></i> تفاصيل طلب الإجازة</h2>
        <div>
            {% if leave.status.name == 'PENDING' %}
            <a href="{{ url_for('leaves.edit', id=leave.id) }}" class="btn btn-warning">
                <i class="fas fa-edit"></i> تعديل
            </a>
            {% endif %}
            <a href="{{ url_for('leaves.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة إلى قائمة الإجازات
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> معلومات الإجازة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">الموظف:</h6>
                            <p>{{ leave.employee.name }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">الرقم العسكري:</h6>
                            <p>{{ leave.employee.military_id }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">نوع الإجازة:</h6>
                            <p><span class="badge" style="background-color: {{ leave.leave_type_rel.color }}">{{ leave.leave_type_rel.name }}</span></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">الحالة:</h6>
                            <p>
                                {% if leave.status.name == 'PENDING' %}
                                <span class="badge bg-warning">قيد الانتظار</span>
                                {% elif leave.status.name == 'APPROVED' %}
                                <span class="badge bg-success">موافق عليها</span>
                                {% elif leave.status.name == 'REJECTED' %}
                                <span class="badge bg-danger">مرفوضة</span>
                                {% elif leave.status.name == 'CANCELLED' %}
                                <span class="badge bg-secondary">ملغية</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">تاريخ البداية:</h6>
                            <p>{{ leave.start_date.strftime('%Y-%m-%d') }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">تاريخ النهاية:</h6>
                            <p>{{ leave.end_date.strftime('%Y-%m-%d') }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">عدد الأيام:</h6>
                            <p>{{ leave.total_days }} يوم</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">تاريخ الطلب:</h6>
                            <p>{{ leave.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        </div>
                        {% if leave.reason %}
                        <div class="col-md-12 mb-3">
                            <h6 class="fw-bold">سبب الإجازة:</h6>
                            <p>{{ leave.reason }}</p>
                        </div>
                        {% endif %}
                        {% if leave.status.name == 'APPROVED' and leave.approved_by %}
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">تمت الموافقة بواسطة:</h6>
                            <p>{{ leave.approver.username }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="fw-bold">تاريخ الموافقة:</h6>
                            <p>{{ leave.approved_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        </div>
                        {% endif %}
                        {% if leave.status.name == 'REJECTED' and leave.rejection_reason %}
                        <div class="col-md-12 mb-3">
                            <h6 class="fw-bold">سبب الرفض:</h6>
                            <p>{{ leave.rejection_reason }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user"></i> معلومات الموظف</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        {% if leave.employee.profile_image %}
                        <img src="{{ url_for('static', filename='img/employees/' + leave.employee.profile_image) }}" alt="{{ leave.employee.name }}" class="rounded-circle img-thumbnail" style="width: 100px; height: 100px; object-fit: cover;">
                        {% else %}
                        <img src="{{ url_for('static', filename='img/default_profile.png') }}" alt="{{ leave.employee.name }}" class="rounded-circle img-thumbnail" style="width: 100px; height: 100px; object-fit: cover;">
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <h6 class="fw-bold">الاسم:</h6>
                        <p>{{ leave.employee.name }}</p>
                    </div>

                    <div class="mb-3">
                        <h6 class="fw-bold">الرتبة:</h6>
                        <p>{{ leave.employee.military_rank or 'غير محدد' }}</p>
                    </div>

                    <div class="mb-3">
                        <h6 class="fw-bold">الوحدة:</h6>
                        <p>{{ leave.employee.unit }}</p>
                    </div>

                    <div class="mb-3">
                        <h6 class="fw-bold">العمل المكلف به:</h6>
                        <p>{{ leave.employee.position }}</p>
                    </div>

                    <div class="mb-3">
                        <h6 class="fw-bold">رصيد الإجازة:</h6>
                        <p>{{ leave.employee.leave_balance }} يوم</p>
                    </div>

                    <div class="d-grid">
                        <a href="{{ url_for('employees.view', id=leave.employee.id) }}" class="btn btn-info">
                            <i class="fas fa-user"></i> عرض بيانات الموظف
                        </a>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-cogs"></i> الإجراءات</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if leave.status.name == 'PENDING' %}
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#approveModal">
                            <i class="fas fa-check"></i> موافقة
                        </button>
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#rejectModal">
                            <i class="fas fa-times"></i> رفض
                        </button>
                        {% endif %}

                        {% if leave.status.name != 'CANCELLED' %}
                        <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#cancelModal">
                            <i class="fas fa-ban"></i> إلغاء الطلب
                        </button>
                        {% endif %}

                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Approve Modal -->
    <div class="modal fade" id="approveModal" tabindex="-1" aria-labelledby="approveModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="approveModalLabel">تأكيد الموافقة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="{{ url_for('leaves.approve', id=leave.id) }}" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="modal-body">
                        <p>هل أنت متأكد من الموافقة على طلب الإجازة للموظف <strong>{{ leave.employee.name }}</strong>؟</p>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="deduct_from_balance" value="true" id="deductFromBalance" checked>
                            <label class="form-check-label" for="deductFromBalance">
                                خصم من رصيد الإجازة ({{ leave.employee.leave_balance }} يوم)
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-success">موافقة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Reject Modal -->
    <div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="rejectModalLabel">تأكيد الرفض</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="{{ url_for('leaves.reject', id=leave.id) }}" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="modal-body">
                        <p>هل أنت متأكد من رفض طلب الإجازة للموظف <strong>{{ leave.employee.name }}</strong>؟</p>
                        <div class="mb-3">
                            <label for="rejectionReason" class="form-label">سبب الرفض</label>
                            <textarea class="form-control" id="rejectionReason" name="rejection_reason" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">رفض</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Cancel Modal -->
    <div class="modal fade" id="cancelModal" tabindex="-1" aria-labelledby="cancelModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="cancelModalLabel">تأكيد الإلغاء</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="{{ url_for('leaves.cancel', id=leave.id) }}" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="modal-body">
                        <p>هل أنت متأكد من إلغاء طلب الإجازة للموظف <strong>{{ leave.employee.name }}</strong>؟</p>
                        {% if leave.status.name == 'APPROVED' %}
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="restore_balance" value="true" id="restoreBalance" checked>
                            <label class="form-check-label" for="restoreBalance">
                                استعادة رصيد الإجازة ({{ leave.total_days }} يوم)
                            </label>
                        </div>
                        {% endif %}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-warning">إلغاء الطلب</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    هل أنت متأكد من حذف طلب الإجازة للموظف <strong>{{ leave.employee.name }}</strong>؟
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form action="{{ url_for('leaves.delete', id=leave.id) }}" method="POST">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
