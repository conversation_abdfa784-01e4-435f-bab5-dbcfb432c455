Defaulting to user installation because normal site-packages is not writeable
Collecting Flask-Limiter
  Downloading flask_limiter-3.12-py3-none-any.whl.metadata (6.3 kB)
Collecting limits>=3.13 (from Flask-Limiter)
  Downloading limits-5.2.0-py3-none-any.whl.metadata (10 kB)
Requirement already satisfied: Flask>=2 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from Flask-Limiter) (2.3.3)
Collecting ordered-set<5,>4 (from Flask-Limiter)
  Downloading ordered_set-4.1.0-py3-none-any.whl.metadata (5.3 kB)
Collecting rich<14,>=12 (from Flask-Limiter)
  Using cached rich-13.9.4-py3-none-any.whl.metadata (18 kB)
Requirement already satisfied: Werkzeug>=2.3.7 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from Flask>=2->Flask-Limiter) (3.1.3)
Requirement already satisfied: Jinja2>=3.1.2 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from Flask>=2->Flask-Limiter) (3.1.6)
Requirement already satisfied: itsdangerous>=2.1.2 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from Flask>=2->Flask-Limiter) (2.2.0)
Requirement already satisfied: click>=8.1.3 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from Flask>=2->Flask-Limiter) (8.2.1)
Requirement already satisfied: blinker>=1.6.2 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from Flask>=2->Flask-Limiter) (1.9.0)
Collecting deprecated>=1.2 (from limits>=3.13->Flask-Limiter)
  Downloading Deprecated-1.2.18-py2.py3-none-any.whl.metadata (5.7 kB)
Requirement already satisfied: packaging<26,>=21 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from limits>=3.13->Flask-Limiter) (25.0)
Requirement already satisfied: typing_extensions in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from limits>=3.13->Flask-Limiter) (4.14.0)
Collecting markdown-it-py>=2.2.0 (from rich<14,>=12->Flask-Limiter)
  Using cached markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from rich<14,>=12->Flask-Limiter) (2.19.1)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from click>=8.1.3->Flask>=2->Flask-Limiter) (0.4.6)
Collecting wrapt<2,>=1.10 (from deprecated>=1.2->limits>=3.13->Flask-Limiter)
  Downloading wrapt-1.17.2-cp311-cp311-win_amd64.whl.metadata (6.5 kB)
Requirement already satisfied: MarkupSafe>=2.0 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from Jinja2>=3.1.2->Flask>=2->Flask-Limiter) (3.0.2)
Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich<14,>=12->Flask-Limiter)
  Using cached mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)
Downloading flask_limiter-3.12-py3-none-any.whl (28 kB)
