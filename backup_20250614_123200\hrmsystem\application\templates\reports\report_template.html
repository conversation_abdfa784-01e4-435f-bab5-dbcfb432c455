{% extends 'layouts/base_print.html' %}

{% block styles %}
<style>
    @page {
        size: A4 portrait;
        margin: 1.5cm;
    }
    body {
        font-family: 'Cairo', sans-serif;
        font-size: 14px;
        direction: rtl;
        text-align: right;
        background-color: white;
        color: black;
    }
    .report-container {
        width: 100%;
        max-width: 21cm;
        margin: 0 auto;
        padding: 0;
        box-sizing: border-box;
    }
    .report-header {
        text-align: center;
        margin-bottom: 20px;
        border: 2px solid black;
        padding: 10px;
    }
    .report-header h1 {
        font-size: 18px;
        font-weight: bold;
        margin: 5px 0;
    }
    .report-header h2 {
        font-size: 16px;
        font-weight: bold;
        margin: 5px 0;
    }
    .report-header h3 {
        font-size: 14px;
        font-weight: bold;
        margin: 5px 0;
        background-color: #ccc;
        padding: 5px;
    }
    .report-title {
        text-align: center;
        margin: 15px 0;
        font-size: 16px;
        font-weight: bold;
    }
    .report-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }
    .report-table th, .report-table td {
        border: 1px solid black;
        padding: 8px;
        text-align: center;
    }
    .report-table th {
        background-color: #b8cce4; /* لون أزرق فاتح كما في الصورة */
        font-weight: bold;
    }
    .report-footer {
        display: flex;
        justify-content: space-between;
        margin-top: 30px;
    }
    .report-footer .left {
        text-align: right;
        font-weight: bold;
    }
    .report-footer .right {
        text-align: left;
    }
    .page-number {
        text-align: center;
        margin-top: 20px;
        font-size: 12px;
    }
    @media print {
        .no-print {
            display: none;
        }
    }
    .export-btn {
        margin: 20px 0;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="report-container">
    <!-- زر تصدير إلى Excel - لن يظهر عند الطباعة -->
    <div class="export-btn no-print">
        <a href="{{ url_for('reports.export_report', report_type=report_type, **request.args) }}" class="btn btn-success">
            <i class="fas fa-file-excel"></i> تصدير إلى Excel
        </a>
        <button onclick="window.print()" class="btn btn-info">
            <i class="fas fa-print"></i> طباعة
        </button>
        <a href="{{ url_for('employees.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة
        </a>
    </div>

    <!-- رأس التقرير -->
    <div class="report-header">
        <h1>وزارة الداخلية</h1>
        <h2>مديرية أمن {{ department_name|default('الخمس') }}</h2>
        <h3>قسم المرور والتراخيص {{ department_name|default('الخمس') }}</h3>
    </div>

    <!-- عنوان التقرير -->
    <div class="report-title">
        كشف عن: {{ report_title|default('.....................') }}
    </div>

    <!-- جدول التقرير -->
    <table class="report-table">
        <thead>
            <tr>
                <th width="5%">ت</th>
                <th width="15%">الرقم العسكري</th>
                <th width="30%">الاســــــــــــم</th>
                <th width="15%">الرتبـــة</th>
                <th width="15%">الرقم الوطنـــي</th>
                <th width="20%">ملاحظـــات</th>
            </tr>
        </thead>
        <tbody>
            {% if employees %}
                {% for employee in employees %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ employee.military_id }}</td>
                    <td>{{ employee.name }}</td>
                    <td>{{ employee.military_rank }}</td>
                    <td>{{ employee.national_id }}</td>
                    <td>{{ employee.status.value if employee.status else '' }}</td>
                </tr>
                {% endfor %}
            {% else %}
                {% for i in range(1, 11) %}
                <tr>
                    <td>{{ i }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                {% endfor %}
            {% endif %}
        </tbody>
    </table>

    <!-- ذيل التقرير -->
    <div class="report-footer">
        <div class="left">
            يعتمد
        </div>
        <div class="right">
            اسم المستخدم / {{ current_user.username }} ............... التاريخ / {{ now.strftime('%Y-%m-%d') }}
        </div>
    </div>

    <!-- رقم الصفحة -->
    <div class="page-number">
        {{ page_number|default('1') }}
    </div>
</div>
{% endblock %}
