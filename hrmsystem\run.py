from application import create_app, db
from application.models import User, Employee, LeaveType, LeaveRequest, AuditLog, UserRole, Department

app = create_app()

# Create a context for database operations
with app.app_context():
    # Create all tables
    db.create_all()

    # Create default departments if they don't exist
    if not Department.query.first():
        default_departments = [
            Department(name='القيادة العامة', description='وحدة القيادة العامة للقوات المسلحة'),
            Department(name='الكتيبة الأولى', description='وحدة الكتيبة الأولى'),
            Department(name='الكتيبة الثانية', description='وحدة الكتيبة الثانية'),
            Department(name='الإدارة العامة', description='وحدة الإدارة العامة')
        ]
        for dept in default_departments:
            db.session.add(dept)
        db.session.commit()
        print('Default departments created!')

    # Check if admin user exists, if not create one
    if not User.query.filter_by(username='admin').first():
        admin = User(
            username='admin',
            email='<EMAIL>',
            role=UserRole.ADMIN,
            is_admin=True,
            full_name='مدير النظام'
        )
        admin.set_password('admin123')
        db.session.add(admin)
        db.session.commit()
        print('Admin user created!')

    # Check if leave types exist, if not create default ones
    if not LeaveType.query.first():
        leave_types = [
            LeaveType(name='إجازة سنوية', description='إجازة سنوية مدفوعة', color='#28a745'),
            LeaveType(name='إجازة مرضية', description='إجازة مرضية', color='#dc3545'),
            LeaveType(name='إجازة طارئة', description='إجازة طارئة', color='#ffc107'),
            LeaveType(name='إجازة بدون راتب', description='إجازة بدون راتب', color='#6c757d')
        ]
        for leave_type in leave_types:
            db.session.add(leave_type)
        db.session.commit()
        print('Default leave types created!')

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
