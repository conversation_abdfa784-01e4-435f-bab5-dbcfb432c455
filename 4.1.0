Defaulting to user installation because normal site-packages is not writeable
Collecting pytest-cov
  Downloading pytest_cov-6.2.1-py3-none-any.whl.metadata (30 kB)
Requirement already satisfied: pytest>=6.2.5 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pytest-cov) (8.4.0)
Collecting coverage>=7.5 (from coverage[toml]>=7.5->pytest-cov)
  Downloading coverage-7.9.1-cp311-cp311-win_amd64.whl.metadata (9.1 kB)
Requirement already satisfied: pluggy>=1.2 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pytest-cov) (1.6.0)
Requirement already satisfied: colorama>=0.4 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pytest>=6.2.5->pytest-cov) (0.4.6)
Requirement already satisfied: iniconfig>=1 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pytest>=6.2.5->pytest-cov) (2.1.0)
Requirement already satisfied: packaging>=20 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pytest>=6.2.5->pytest-cov) (25.0)
Requirement already satisfied: pygments>=2.7.2 in c:\users\<USER>\appdata\roaming\python\python311\site-packages (from pytest>=6.2.5->pytest-cov) (2.19.1)
Downloading pytest_cov-6.2.1-py3-none-any.whl (24 kB)
Downloading coverage-7.9.1-cp311-cp311-win_amd64.whl (215 kB)
   --------------------------------------- 215.5/215.5 kB 51.1 kB/s eta 0:00:00
Installing collected packages: coverage, pytest-cov
Successfully installed coverage-7.9.1 pytest-cov-6.2.1
