#!/usr/bin/env python3
"""
تحسين التقارير المطبوعة لنظام إدارة الموارد البشرية
"""
import os
import shutil
from datetime import datetime

class ReportImprover:
    def __init__(self):
        self.improvements_made = []
        
    def improve_employee_reports(self):
        """تحسين تقارير الموظفين"""
        print("📊 تحسين تقارير الموظفين...")
        
        # إنشاء CSS محسن للطباعة
        self._create_print_css()
        
        # تحسين قوالب التقارير
        self._improve_employee_report_template()
        
        # إضافة قالب تقرير موظف فردي
        self._create_individual_employee_report()
        
    def improve_leave_reports(self):
        """تحسين تقارير الإجازات"""
        print("📅 تحسين تقارير الإجازات...")

        # تحسين قالب تقرير الإجازات
        self._improve_leave_report_template()

        # إضافة تقرير إجازات شهري
        self._create_monthly_leave_report()

        # إضافة تقرير إجازة فردي
        self._create_individual_leave_report()

    def _improve_leave_report_template(self):
        """تحسين قالب تقرير الإجازات"""
        print("  📋 تحسين قالب تقرير الإجازات...")

        template_content = """{% extends "layouts/base.html" %}

{% block title %}تقرير الإجازات{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/print.css') }}">
{% endblock %}

{% block content %}
<div class="print-preview">
    <!-- رأس التقرير -->
    <div class="print-header">
        <img src="{{ url_for('static', filename='img/logo.png') }}" alt="شعار المؤسسة" class="logo">
        <h1>تقرير الإجازات</h1>
        <div class="date">تاريخ الطباعة: {{ now.strftime('%Y-%m-%d %H:%M') }}</div>
        {% if date_from and date_to %}
        <div class="date">الفترة: من {{ date_from }} إلى {{ date_to }}</div>
        {% endif %}
    </div>

    <!-- إحصائيات الإجازات -->
    <div class="report-info no-page-break">
        <h2>إحصائيات الإجازات</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ total_requests }}</div>
                <div class="stat-label">إجمالي الطلبات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ approved_requests }}</div>
                <div class="stat-label">الطلبات المعتمدة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ pending_requests }}</div>
                <div class="stat-label">الطلبات المعلقة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ rejected_requests }}</div>
                <div class="stat-label">الطلبات المرفوضة</div>
            </div>
        </div>
    </div>

    <!-- جدول الإجازات -->
    <div class="leaves-table">
        <h2>تفاصيل الإجازات</h2>
        <table>
            <thead>
                <tr>
                    <th>الموظف</th>
                    <th>نوع الإجازة</th>
                    <th>تاريخ البداية</th>
                    <th>تاريخ النهاية</th>
                    <th>عدد الأيام</th>
                    <th>الحالة</th>
                    <th>تاريخ الطلب</th>
                    <th>ملاحظات</th>
                </tr>
            </thead>
            <tbody>
                {% for leave in leaves %}
                <tr>
                    <td>{{ leave.employee.name }}</td>
                    <td>{{ leave.leave_type_rel.name }}</td>
                    <td>{{ leave.start_date.strftime('%Y-%m-%d') }}</td>
                    <td>{{ leave.end_date.strftime('%Y-%m-%d') }}</td>
                    <td>{{ leave.days_count }}</td>
                    <td>
                        {% if leave.status == 'approved' %}
                        <span style="color: green;">معتمدة</span>
                        {% elif leave.status == 'pending' %}
                        <span style="color: orange;">معلقة</span>
                        {% elif leave.status == 'rejected' %}
                        <span style="color: red;">مرفوضة</span>
                        {% endif %}
                    </td>
                    <td>{{ leave.created_at.strftime('%Y-%m-%d') }}</td>
                    <td>{{ leave.notes or '-' }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- التوقيعات -->
    <div class="signatures">
        <div class="signature-box">
            <div>مدير الموارد البشرية</div>
        </div>
        <div class="signature-box">
            <div>مدير الإدارة</div>
        </div>
        <div class="signature-box">
            <div>القائد العام</div>
        </div>
    </div>
</div>

<!-- أزرار الطباعة -->
<div class="no-print">
    <button onclick="window.print()" class="btn btn-primary print-button">
        <i class="fas fa-print"></i> طباعة التقرير
    </button>
</div>
{% endblock %}"""

        template_file = 'hrmsystem/application/templates/reports/leaves_print.html'
        os.makedirs(os.path.dirname(template_file), exist_ok=True)

        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(template_content)

        self.improvements_made.append(f"تم تحسين قالب تقرير الإجازات: {template_file}")
        print(f"    ✅ تم إنشاء: {template_file}")

    def _create_monthly_leave_report(self):
        """إنشاء تقرير إجازات شهري"""
        print("  📅 إنشاء تقرير إجازات شهري...")

        template_content = """{% extends "layouts/base.html" %}

{% block title %}التقرير الشهري للإجازات{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/print.css') }}">
{% endblock %}

{% block content %}
<div class="print-preview">
    <!-- رأس التقرير -->
    <div class="print-header">
        <img src="{{ url_for('static', filename='img/logo.png') }}" alt="شعار المؤسسة" class="logo">
        <h1>التقرير الشهري للإجازات</h1>
        <div class="date">الشهر: {{ month_name }} {{ year }}</div>
        <div class="date">تاريخ الطباعة: {{ now.strftime('%Y-%m-%d %H:%M') }}</div>
    </div>

    <!-- ملخص الشهر -->
    <div class="report-info no-page-break">
        <h2>ملخص شهر {{ month_name }}</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ monthly_stats.total_days }}</div>
                <div class="stat-label">إجمالي أيام الإجازة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ monthly_stats.employees_on_leave }}</div>
                <div class="stat-label">الموظفين في إجازة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ monthly_stats.new_requests }}</div>
                <div class="stat-label">الطلبات الجديدة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ monthly_stats.processed_requests }}</div>
                <div class="stat-label">الطلبات المعالجة</div>
            </div>
        </div>
    </div>

    <!-- الإجازات حسب النوع -->
    <div class="leave-types-summary">
        <h2>الإجازات حسب النوع</h2>
        <table>
            <thead>
                <tr>
                    <th>نوع الإجازة</th>
                    <th>عدد الطلبات</th>
                    <th>إجمالي الأيام</th>
                    <th>متوسط المدة</th>
                </tr>
            </thead>
            <tbody>
                {% for leave_type in leave_types_summary %}
                <tr>
                    <td>{{ leave_type.name }}</td>
                    <td>{{ leave_type.count }}</td>
                    <td>{{ leave_type.total_days }}</td>
                    <td>{{ "%.1f"|format(leave_type.avg_days) }} يوم</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- الإجازات حسب الوحدة -->
    <div class="units-summary page-break">
        <h2>الإجازات حسب الوحدة</h2>
        <table>
            <thead>
                <tr>
                    <th>الوحدة</th>
                    <th>عدد الموظفين</th>
                    <th>الموظفين في إجازة</th>
                    <th>النسبة المئوية</th>
                </tr>
            </thead>
            <tbody>
                {% for unit in units_summary %}
                <tr>
                    <td>{{ unit.name }}</td>
                    <td>{{ unit.total_employees }}</td>
                    <td>{{ unit.employees_on_leave }}</td>
                    <td>{{ "%.1f"|format(unit.percentage) }}%</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- التوقيعات -->
    <div class="signatures">
        <div class="signature-box">
            <div>مدير الموارد البشرية</div>
        </div>
        <div class="signature-box">
            <div>مدير الإدارة</div>
        </div>
        <div class="signature-box">
            <div>القائد العام</div>
        </div>
    </div>
</div>

<!-- أزرار الطباعة -->
<div class="no-print">
    <button onclick="window.print()" class="btn btn-primary print-button">
        <i class="fas fa-print"></i> طباعة التقرير الشهري
    </button>
</div>
{% endblock %}"""

        template_file = 'hrmsystem/application/templates/reports/monthly_leaves_print.html'
        os.makedirs(os.path.dirname(template_file), exist_ok=True)

        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(template_content)

        self.improvements_made.append(f"تم إنشاء التقرير الشهري للإجازات: {template_file}")
        print(f"    ✅ تم إنشاء: {template_file}")

    def _create_individual_leave_report(self):
        """إنشاء تقرير إجازة فردي"""
        print("  📄 إنشاء تقرير إجازة فردي...")

        template_content = """{% extends "layouts/base.html" %}

{% block title %}طلب إجازة - {{ leave.employee.name }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/print.css') }}">
{% endblock %}

{% block content %}
<div class="print-preview">
    <!-- رأس التقرير -->
    <div class="print-header">
        <img src="{{ url_for('static', filename='img/logo.png') }}" alt="شعار المؤسسة" class="logo">
        <h1>طلب إجازة</h1>
        <div class="date">رقم الطلب: {{ leave.id }}</div>
        <div class="date">تاريخ الطباعة: {{ now.strftime('%Y-%m-%d %H:%M') }}</div>
    </div>

    <!-- بيانات الموظف -->
    <div class="employee-info">
        <h3>بيانات الموظف</h3>
        <table style="width: 100%; border: none;">
            <tr>
                <td style="border: none; font-weight: bold; width: 25%;">الاسم:</td>
                <td style="border: none; width: 25%;">{{ leave.employee.name }}</td>
                <td style="border: none; font-weight: bold; width: 25%;">الرقم العسكري:</td>
                <td style="border: none; width: 25%;">{{ leave.employee.military_id }}</td>
            </tr>
            <tr>
                <td style="border: none; font-weight: bold;">الرتبة:</td>
                <td style="border: none;">{{ leave.employee.military_rank or '-' }}</td>
                <td style="border: none; font-weight: bold;">الوحدة:</td>
                <td style="border: none;">{{ leave.employee.unit or '-' }}</td>
            </tr>
            <tr>
                <td style="border: none; font-weight: bold;">المنصب:</td>
                <td style="border: none;">{{ leave.employee.position or '-' }}</td>
                <td style="border: none; font-weight: bold;">رقم الهاتف:</td>
                <td style="border: none;">{{ leave.employee.phone or '-' }}</td>
            </tr>
        </table>
    </div>

    <!-- تفاصيل الإجازة -->
    <div class="employee-info">
        <h3>تفاصيل الإجازة</h3>
        <table style="width: 100%; border: none;">
            <tr>
                <td style="border: none; font-weight: bold; width: 25%;">نوع الإجازة:</td>
                <td style="border: none; width: 25%;">{{ leave.leave_type_rel.name }}</td>
                <td style="border: none; font-weight: bold; width: 25%;">تاريخ البداية:</td>
                <td style="border: none; width: 25%;">{{ leave.start_date.strftime('%Y-%m-%d') }}</td>
            </tr>
            <tr>
                <td style="border: none; font-weight: bold;">تاريخ النهاية:</td>
                <td style="border: none;">{{ leave.end_date.strftime('%Y-%m-%d') }}</td>
                <td style="border: none; font-weight: bold;">عدد الأيام:</td>
                <td style="border: none;">{{ leave.days_count }} يوم</td>
            </tr>
            <tr>
                <td style="border: none; font-weight: bold;">تاريخ الطلب:</td>
                <td style="border: none;">{{ leave.created_at.strftime('%Y-%m-%d') }}</td>
                <td style="border: none; font-weight: bold;">الحالة:</td>
                <td style="border: none;">
                    {% if leave.status == 'approved' %}
                    <span style="color: green; font-weight: bold;">معتمدة</span>
                    {% elif leave.status == 'pending' %}
                    <span style="color: orange; font-weight: bold;">معلقة</span>
                    {% elif leave.status == 'rejected' %}
                    <span style="color: red; font-weight: bold;">مرفوضة</span>
                    {% endif %}
                </td>
            </tr>
        </table>
    </div>

    <!-- سبب الإجازة -->
    {% if leave.reason %}
    <div class="employee-info">
        <h3>سبب الإجازة</h3>
        <p style="margin: 0; padding: 5mm; background: #f9f9f9; border: 1px solid #ddd;">
            {{ leave.reason }}
        </p>
    </div>
    {% endif %}

    <!-- ملاحظات -->
    {% if leave.notes %}
    <div class="employee-info">
        <h3>ملاحظات</h3>
        <p style="margin: 0; padding: 5mm; background: #f9f9f9; border: 1px solid #ddd;">
            {{ leave.notes }}
        </p>
    </div>
    {% endif %}

    <!-- معلومات الاعتماد -->
    {% if leave.status == 'approved' and leave.approved_by %}
    <div class="employee-info">
        <h3>معلومات الاعتماد</h3>
        <table style="width: 100%; border: none;">
            <tr>
                <td style="border: none; font-weight: bold; width: 25%;">معتمد بواسطة:</td>
                <td style="border: none; width: 25%;">{{ leave.approved_by.full_name or leave.approved_by.username }}</td>
                <td style="border: none; font-weight: bold; width: 25%;">تاريخ الاعتماد:</td>
                <td style="border: none; width: 25%;">{{ leave.approved_at.strftime('%Y-%m-%d %H:%M') if leave.approved_at else '-' }}</td>
            </tr>
        </table>
    </div>
    {% endif %}

    <!-- التوقيعات -->
    <div class="signatures">
        <div class="signature-box">
            <div>توقيع الموظف</div>
            <div style="margin-top: 5mm; font-size: 9pt;">{{ leave.employee.name }}</div>
        </div>
        <div class="signature-box">
            <div>القائد المباشر</div>
        </div>
        <div class="signature-box">
            <div>مدير الموارد البشرية</div>
        </div>
    </div>

    <!-- ختم الموافقة -->
    {% if leave.status == 'approved' %}
    <div style="position: absolute; top: 50mm; right: 20mm;
                border: 3px solid green; padding: 10mm; text-align: center;
                transform: rotate(-15deg); color: green; font-weight: bold; font-size: 14pt;">
        معتمد<br>
        <div style="font-size: 10pt;">{{ leave.approved_at.strftime('%Y-%m-%d') if leave.approved_at else '' }}</div>
    </div>
    {% endif %}
</div>

<!-- أزرار الطباعة -->
<div class="no-print">
    <button onclick="window.print()" class="btn btn-primary print-button">
        <i class="fas fa-print"></i> طباعة طلب الإجازة
    </button>
</div>
{% endblock %}"""

        template_file = 'hrmsystem/application/templates/leaves/leave_request_print.html'
        os.makedirs(os.path.dirname(template_file), exist_ok=True)

        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(template_content)

        self.improvements_made.append(f"تم إنشاء تقرير طلب الإجازة الفردي: {template_file}")
        print(f"    ✅ تم إنشاء: {template_file}")
        
    def _create_print_css(self):
        """إنشاء CSS محسن للطباعة"""
        print("  🎨 إنشاء CSS محسن للطباعة...")
        
        css_content = """
/* CSS محسن للطباعة - نظام إدارة الموارد البشرية */

@media print {
    /* إعدادات عامة للطباعة */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }
    
    body {
        font-family: 'Arial', 'Tahoma', sans-serif;
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: white;
        margin: 0;
        padding: 20mm;
    }
    
    /* إخفاء عناصر غير مطلوبة للطباعة */
    .no-print,
    .btn,
    .navbar,
    .sidebar,
    .pagination,
    .form-group,
    input[type="submit"],
    button {
        display: none !important;
    }
    
    /* رأس الصفحة */
    .print-header {
        text-align: center;
        border-bottom: 2px solid #333;
        padding-bottom: 10mm;
        margin-bottom: 10mm;
    }
    
    .print-header h1 {
        font-size: 18pt;
        font-weight: bold;
        margin: 0;
        color: #333;
    }
    
    .print-header .logo {
        max-height: 60px;
        margin-bottom: 10px;
    }
    
    .print-header .date {
        font-size: 10pt;
        color: #666;
        margin-top: 5mm;
    }
    
    /* الجداول */
    table {
        width: 100%;
        border-collapse: collapse;
        margin: 5mm 0;
        font-size: 10pt;
    }
    
    table th,
    table td {
        border: 1px solid #333;
        padding: 3mm;
        text-align: right;
        vertical-align: top;
    }
    
    table th {
        background-color: #f0f0f0 !important;
        font-weight: bold;
        font-size: 11pt;
    }
    
    table tr:nth-child(even) {
        background-color: #f9f9f9 !important;
    }
    
    /* العناوين */
    h1, h2, h3, h4, h5, h6 {
        color: #333;
        page-break-after: avoid;
        margin-top: 10mm;
        margin-bottom: 5mm;
    }
    
    h1 { font-size: 16pt; }
    h2 { font-size: 14pt; }
    h3 { font-size: 12pt; }
    
    /* الفقرات */
    p {
        margin: 3mm 0;
        text-align: justify;
    }
    
    /* كسر الصفحات */
    .page-break {
        page-break-before: always;
    }
    
    .page-break-after {
        page-break-after: always;
    }
    
    .no-page-break {
        page-break-inside: avoid;
    }
    
    /* ذيل الصفحة */
    .print-footer {
        position: fixed;
        bottom: 10mm;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 9pt;
        color: #666;
        border-top: 1px solid #ccc;
        padding-top: 3mm;
    }
    
    /* معلومات الموظف */
    .employee-info {
        background: #f8f9fa !important;
        border: 1px solid #333;
        padding: 5mm;
        margin: 5mm 0;
        border-radius: 0;
    }
    
    .employee-info h3 {
        margin-top: 0;
        color: #333;
        border-bottom: 1px solid #666;
        padding-bottom: 2mm;
    }
    
    /* الإحصائيات */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 5mm;
        margin: 5mm 0;
    }
    
    .stat-card {
        border: 1px solid #333;
        padding: 3mm;
        text-align: center;
        background: #f8f9fa !important;
    }
    
    .stat-number {
        font-size: 16pt;
        font-weight: bold;
        color: #333;
    }
    
    .stat-label {
        font-size: 9pt;
        color: #666;
    }
    
    /* التوقيعات */
    .signatures {
        margin-top: 20mm;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 10mm;
    }
    
    .signature-box {
        text-align: center;
        border-top: 1px solid #333;
        padding-top: 3mm;
        margin-top: 15mm;
    }
    
    /* الباركود أو QR Code */
    .qr-code {
        position: absolute;
        top: 10mm;
        left: 10mm;
        width: 20mm;
        height: 20mm;
    }
    
    /* تحسين النصوص العربية */
    .arabic-text {
        direction: rtl;
        text-align: right;
        font-family: 'Arial', 'Tahoma', sans-serif;
    }
    
    /* رقم الصفحة */
    @page {
        margin: 20mm;
        @bottom-center {
            content: "صفحة " counter(page) " من " counter(pages);
            font-size: 9pt;
            color: #666;
        }
    }
}

/* إعدادات خاصة للشاشة */
@media screen {
    .print-preview {
        max-width: 210mm;
        margin: 0 auto;
        background: white;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        padding: 20mm;
    }
    
    .print-button {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
    }
    
    .print-button:hover {
        background: #0056b3;
    }
}
"""
        
        css_file = 'hrmsystem/application/static/css/print.css'
        os.makedirs(os.path.dirname(css_file), exist_ok=True)
        
        with open(css_file, 'w', encoding='utf-8') as f:
            f.write(css_content)
        
        self.improvements_made.append(f"تم إنشاء CSS محسن للطباعة: {css_file}")
        print(f"    ✅ تم إنشاء: {css_file}")
    
    def _improve_employee_report_template(self):
        """تحسين قالب تقرير الموظفين"""
        print("  📋 تحسين قالب تقرير الموظفين...")
        
        template_content = """{% extends "layouts/base.html" %}

{% block title %}تقرير الموظفين{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/print.css') }}">
{% endblock %}

{% block content %}
<div class="print-preview">
    <!-- رأس التقرير -->
    <div class="print-header">
        <img src="{{ url_for('static', filename='img/logo.png') }}" alt="شعار المؤسسة" class="logo">
        <h1>تقرير الموظفين</h1>
        <div class="date">تاريخ الطباعة: {{ now.strftime('%Y-%m-%d %H:%M') }}</div>
    </div>

    <!-- معلومات التقرير -->
    <div class="report-info no-page-break">
        <h2>معلومات التقرير</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ total_employees }}</div>
                <div class="stat-label">إجمالي الموظفين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ active_employees }}</div>
                <div class="stat-label">الموظفين النشطين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ departments_count }}</div>
                <div class="stat-label">عدد الأقسام</div>
            </div>
        </div>
    </div>

    <!-- جدول الموظفين -->
    <div class="employees-table">
        <h2>قائمة الموظفين</h2>
        <table>
            <thead>
                <tr>
                    <th>الرقم العسكري</th>
                    <th>الاسم</th>
                    <th>الرتبة</th>
                    <th>الوحدة</th>
                    <th>المنصب</th>
                    <th>الحالة</th>
                    <th>تاريخ التعيين</th>
                </tr>
            </thead>
            <tbody>
                {% for employee in employees %}
                <tr>
                    <td>{{ employee.military_id }}</td>
                    <td>{{ employee.name }}</td>
                    <td>{{ employee.military_rank or '-' }}</td>
                    <td>{{ employee.unit or '-' }}</td>
                    <td>{{ employee.position or '-' }}</td>
                    <td>{{ employee.status.value if employee.status else '-' }}</td>
                    <td>{{ employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else '-' }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- التوقيعات -->
    <div class="signatures">
        <div class="signature-box">
            <div>مدير الموارد البشرية</div>
        </div>
        <div class="signature-box">
            <div>مدير الإدارة</div>
        </div>
        <div class="signature-box">
            <div>القائد العام</div>
        </div>
    </div>

    <!-- ذيل التقرير -->
    <div class="print-footer">
        <div>نظام إدارة الموارد البشرية - {{ now.year }}</div>
    </div>
</div>

<!-- أزرار الطباعة (تظهر على الشاشة فقط) -->
<div class="no-print">
    <button onclick="window.print()" class="btn btn-primary print-button">
        <i class="fas fa-print"></i> طباعة التقرير
    </button>
</div>

<script>
// تحسين الطباعة
window.addEventListener('beforeprint', function() {
    document.title = 'تقرير_الموظفين_' + new Date().toISOString().split('T')[0];
});

// إضافة رقم الصفحة
window.addEventListener('load', function() {
    if (window.matchMedia('print').matches) {
        // إعدادات خاصة بالطباعة
    }
});
</script>
{% endblock %}"""
        
        template_file = 'hrmsystem/application/templates/reports/employees_print.html'
        os.makedirs(os.path.dirname(template_file), exist_ok=True)
        
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        self.improvements_made.append(f"تم تحسين قالب تقرير الموظفين: {template_file}")
        print(f"    ✅ تم إنشاء: {template_file}")
    
    def _create_individual_employee_report(self):
        """إنشاء قالب تقرير موظف فردي"""
        print("  👤 إنشاء قالب تقرير موظف فردي...")
        
        template_content = """{% extends "layouts/base.html" %}

{% block title %}تقرير الموظف - {{ employee.name }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/print.css') }}">
{% endblock %}

{% block content %}
<div class="print-preview">
    <!-- رأس التقرير -->
    <div class="print-header">
        <img src="{{ url_for('static', filename='img/logo.png') }}" alt="شعار المؤسسة" class="logo">
        <h1>بطاقة بيانات الموظف</h1>
        <div class="date">تاريخ الطباعة: {{ now.strftime('%Y-%m-%d %H:%M') }}</div>
    </div>

    <!-- صورة الموظف -->
    <div style="float: left; margin: 0 10mm 10mm 0;">
        {% if employee.profile_image %}
        <img src="{{ url_for('static', filename='uploads/' + employee.profile_image) }}" 
             alt="صورة الموظف" style="width: 40mm; height: 50mm; border: 1px solid #333;">
        {% else %}
        <div style="width: 40mm; height: 50mm; border: 1px solid #333; background: #f0f0f0; 
                    display: flex; align-items: center; justify-content: center; font-size: 10pt;">
            لا توجد صورة
        </div>
        {% endif %}
    </div>

    <!-- البيانات الأساسية -->
    <div class="employee-info">
        <h3>البيانات الأساسية</h3>
        <table style="width: 100%; border: none;">
            <tr>
                <td style="border: none; font-weight: bold; width: 30%;">الاسم:</td>
                <td style="border: none;">{{ employee.name }}</td>
            </tr>
            <tr>
                <td style="border: none; font-weight: bold;">الرقم العسكري:</td>
                <td style="border: none;">{{ employee.military_id }}</td>
            </tr>
            <tr>
                <td style="border: none; font-weight: bold;">الرقم الوطني:</td>
                <td style="border: none;">{{ employee.national_id or '-' }}</td>
            </tr>
            <tr>
                <td style="border: none; font-weight: bold;">تاريخ الميلاد:</td>
                <td style="border: none;">{{ employee.date_of_birth.strftime('%Y-%m-%d') if employee.date_of_birth else '-' }}</td>
            </tr>
            <tr>
                <td style="border: none; font-weight: bold;">فصيلة الدم:</td>
                <td style="border: none;">{{ employee.blood_type or '-' }}</td>
            </tr>
        </table>
    </div>

    <div style="clear: both;"></div>

    <!-- البيانات العسكرية -->
    <div class="employee-info">
        <h3>البيانات العسكرية</h3>
        <table style="width: 100%; border: none;">
            <tr>
                <td style="border: none; font-weight: bold; width: 30%;">الرتبة:</td>
                <td style="border: none;">{{ employee.military_rank or '-' }}</td>
            </tr>
            <tr>
                <td style="border: none; font-weight: bold;">الوحدة:</td>
                <td style="border: none;">{{ employee.unit or '-' }}</td>
            </tr>
            <tr>
                <td style="border: none; font-weight: bold;">المنصب:</td>
                <td style="border: none;">{{ employee.position or '-' }}</td>
            </tr>
            <tr>
                <td style="border: none; font-weight: bold;">الفئة:</td>
                <td style="border: none;">{{ employee.category.value if employee.category else '-' }}</td>
            </tr>
            <tr>
                <td style="border: none; font-weight: bold;">تاريخ التعيين:</td>
                <td style="border: none;">{{ employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else '-' }}</td>
            </tr>
            <tr>
                <td style="border: none; font-weight: bold;">الحالة:</td>
                <td style="border: none;">{{ employee.status.value if employee.status else '-' }}</td>
            </tr>
        </table>
    </div>

    <!-- بيانات الاتصال -->
    <div class="employee-info">
        <h3>بيانات الاتصال</h3>
        <table style="width: 100%; border: none;">
            <tr>
                <td style="border: none; font-weight: bold; width: 30%;">رقم الهاتف:</td>
                <td style="border: none;">{{ employee.phone or '-' }}</td>
            </tr>
            <tr>
                <td style="border: none; font-weight: bold;">البريد الإلكتروني:</td>
                <td style="border: none;">{{ employee.email or '-' }}</td>
            </tr>
            <tr>
                <td style="border: none; font-weight: bold;">السكن الحالي:</td>
                <td style="border: none;">{{ employee.current_residence or '-' }}</td>
            </tr>
        </table>
    </div>

    <!-- البيانات المصرفية -->
    <div class="employee-info">
        <h3>البيانات المصرفية</h3>
        <table style="width: 100%; border: none;">
            <tr>
                <td style="border: none; font-weight: bold; width: 30%;">اسم البنك:</td>
                <td style="border: none;">{{ employee.bank_name or '-' }}</td>
            </tr>
            <tr>
                <td style="border: none; font-weight: bold;">رقم الحساب:</td>
                <td style="border: none;">{{ employee.bank_account or '-' }}</td>
            </tr>
        </table>
    </div>

    <!-- التوقيعات -->
    <div class="signatures">
        <div class="signature-box">
            <div>توقيع الموظف</div>
        </div>
        <div class="signature-box">
            <div>مدير الموارد البشرية</div>
        </div>
        <div class="signature-box">
            <div>القائد المباشر</div>
        </div>
    </div>
</div>

<!-- أزرار الطباعة -->
<div class="no-print">
    <button onclick="window.print()" class="btn btn-primary print-button">
        <i class="fas fa-print"></i> طباعة البطاقة
    </button>
</div>
{% endblock %}"""
        
        template_file = 'hrmsystem/application/templates/employees/employee_card_print.html'
        os.makedirs(os.path.dirname(template_file), exist_ok=True)
        
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        self.improvements_made.append(f"تم إنشاء قالب بطاقة الموظف: {template_file}")
        print(f"    ✅ تم إنشاء: {template_file}")
    
    def generate_report(self):
        """إنشاء تقرير التحسينات"""
        print("\n📊 إنشاء تقرير التحسينات...")
        
        report_content = f"""
📋 تقرير تحسين التقارير المطبوعة
═══════════════════════════════════════════════════════════

📅 التاريخ والوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

✅ التحسينات المطبقة:
═══════════════════════════════════════════════════════════
"""
        
        for i, improvement in enumerate(self.improvements_made, 1):
            report_content += f"{i}. {improvement}\n"
        
        report_content += f"""

🎯 الميزات الجديدة:
═══════════════════════════════════════════════════════════
• CSS محسن للطباعة مع دعم كامل للنصوص العربية
• تصميم احترافي للتقارير مع رأس وذيل الصفحة
• دعم شعار المؤسسة في التقارير
• تحسين تنسيق الجداول والنصوص
• إضافة أماكن للتوقيعات
• تحسين كسر الصفحات
• دعم أرقام الصفحات
• تحسين عرض البيانات العربية

📋 التقارير المحسنة:
═══════════════════════════════════════════════════════════
• تقرير الموظفين العام
• بطاقة بيانات الموظف الفردية
• تقارير الإجازات (قيد التطوير)
• التقارير الشهرية (قيد التطوير)

🔧 التوصيات:
═══════════════════════════════════════════════════════════
• اختبار الطباعة على طابعات مختلفة
• إضافة شعار المؤسسة الفعلي
• تخصيص التوقيعات حسب الحاجة
• إضافة المزيد من التقارير المتخصصة

تم إنشاء التقرير بواسطة: أداة تحسين التقارير
الإصدار: 1.0
"""
        
        report_file = f"reports_improvement_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"✅ تم إنشاء تقرير التحسينات: {report_file}")
        return report_file

def main():
    """الدالة الرئيسية"""
    print("📊 أداة تحسين التقارير المطبوعة")
    print("=" * 50)
    
    improver = ReportImprover()
    
    # تحسين تقارير الموظفين
    improver.improve_employee_reports()
    
    # تحسين تقارير الإجازات
    improver.improve_leave_reports()
    
    # إنشاء التقرير
    report_file = improver.generate_report()
    
    print("\n" + "=" * 50)
    print("🎉 تم الانتهاء من تحسين التقارير!")
    print("=" * 50)
    print(f"📊 التحسينات المطبقة: {len(improver.improvements_made)}")
    print(f"📋 التقرير: {report_file}")

if __name__ == '__main__':
    main()
