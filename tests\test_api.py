"""
API tests for HRM System
"""
import pytest
import json
from hrmsystem.application.models import <PERSON><PERSON><PERSON>ee, LeaveRequest, EmployeeStatus
from hrmsystem.application.api.auth import generate_token


class TestAPIAuth:
    """Test API authentication."""
    
    def test_api_login_valid_credentials(self, client, admin_user):
        """Test API login with valid credentials."""
        response = client.post('/api/auth/login', 
                              json={
                                  'username': 'admin_test',
                                  'password': 'admin123'
                              })
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'token' in data
        assert 'user' in data
        assert data['user']['username'] == 'admin_test'
    
    def test_api_login_invalid_credentials(self, client):
        """Test API login with invalid credentials."""
        response = client.post('/api/auth/login',
                              json={
                                  'username': 'invalid',
                                  'password': 'wrong'
                              })
        
        assert response.status_code == 401
        data = response.get_json()
        assert 'message' in data
    
    def test_api_login_missing_data(self, client):
        """Test API login with missing data."""
        response = client.post('/api/auth/login', json={})
        
        assert response.status_code == 400
        data = response.get_json()
        assert 'Username and password required' in data['message']


class TestAPIEmployees:
    """Test API employee endpoints."""
    
    def get_auth_headers(self, user):
        """Helper to get authentication headers."""
        token = generate_token(user.id)
        return {'Authorization': f'Bearer {token}'}
    
    def test_get_employees_without_token(self, client):
        """Test getting employees without authentication token."""
        response = client.get('/api/employees')
        assert response.status_code == 401
    
    def test_get_employees_with_token(self, client, admin_user, sample_employee):
        """Test getting employees with valid token."""
        headers = self.get_auth_headers(admin_user)
        response = client.get('/api/employees', headers=headers)
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'employees' in data
        assert 'pagination' in data
        assert len(data['employees']) > 0
    
    def test_get_employees_with_search(self, client, admin_user, sample_employee):
        """Test searching employees via API."""
        headers = self.get_auth_headers(admin_user)
        response = client.get('/api/employees?search=أحمد', headers=headers)
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'employees' in data
    
    def test_get_employees_with_filters(self, client, admin_user, sample_employee):
        """Test filtering employees via API."""
        headers = self.get_auth_headers(admin_user)
        response = client.get('/api/employees?status=ACTIVE', headers=headers)
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'employees' in data
    
    def test_get_employee_by_id(self, client, admin_user, sample_employee):
        """Test getting specific employee by ID."""
        headers = self.get_auth_headers(admin_user)
        response = client.get(f'/api/employees/{sample_employee.id}', headers=headers)
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'employee' in data
        assert data['employee']['id'] == sample_employee.id
        assert data['employee']['name'] == sample_employee.name
    
    def test_get_nonexistent_employee(self, client, admin_user):
        """Test getting non-existent employee."""
        headers = self.get_auth_headers(admin_user)
        response = client.get('/api/employees/99999', headers=headers)
        
        assert response.status_code == 404
    
    def test_create_employee_valid_data(self, client, admin_user, app):
        """Test creating employee via API with valid data."""
        headers = self.get_auth_headers(admin_user)
        employee_data = {
            'military_id': '54321',
            'name': 'محمد أحمد الجديد',
            'military_rank': 'نقيب',
            'unit': 'الوحدة الثالثة',
            'phone': '0501234567',
            'email': '<EMAIL>'
        }
        
        response = client.post('/api/employees', 
                              json=employee_data, 
                              headers=headers)
        
        assert response.status_code == 201
        data = response.get_json()
        assert 'employee_id' in data
        
        # Verify in database
        with app.app_context():
            employee = Employee.query.filter_by(military_id='54321').first()
            assert employee is not None
            assert employee.name == 'محمد أحمد الجديد'
    
    def test_create_employee_missing_data(self, client, admin_user):
        """Test creating employee with missing required data."""
        headers = self.get_auth_headers(admin_user)
        response = client.post('/api/employees', 
                              json={'name': 'اسم فقط'}, 
                              headers=headers)
        
        assert response.status_code == 400
        data = response.get_json()
        assert 'Military ID and name are required' in data['message']
    
    def test_create_employee_duplicate_military_id(self, client, admin_user, sample_employee):
        """Test creating employee with duplicate military ID."""
        headers = self.get_auth_headers(admin_user)
        employee_data = {
            'military_id': sample_employee.military_id,  # Duplicate
            'name': 'موظف مكرر'
        }
        
        response = client.post('/api/employees', 
                              json=employee_data, 
                              headers=headers)
        
        assert response.status_code == 400
        data = response.get_json()
        assert 'Military ID already exists' in data['message']


class TestAPILeaves:
    """Test API leave endpoints."""
    
    def get_auth_headers(self, user):
        """Helper to get authentication headers."""
        token = generate_token(user.id)
        return {'Authorization': f'Bearer {token}'}
    
    def test_get_leaves_without_token(self, client):
        """Test getting leaves without authentication token."""
        response = client.get('/api/leaves')
        assert response.status_code == 401
    
    def test_get_leaves_with_token(self, client, admin_user):
        """Test getting leaves with valid token."""
        headers = self.get_auth_headers(admin_user)
        response = client.get('/api/leaves', headers=headers)
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'leaves' in data
        assert 'pagination' in data
    
    def test_approve_leave_request(self, client, admin_user, sample_employee, app):
        """Test approving leave request via API."""
        # Create a leave request first
        with app.app_context():
            from hrmsystem.application import db
            from hrmsystem.application.models import LeaveRequest, LeaveStatus
            from datetime import date, timedelta
            
            leave_request = LeaveRequest(
                employee_id=sample_employee.id,
                leave_type_id=1,
                start_date=date.today() + timedelta(days=7),
                end_date=date.today() + timedelta(days=12),
                total_days=5,
                reason='إجازة للاختبار',
                status=LeaveStatus.PENDING
            )
            db.session.add(leave_request)
            db.session.commit()
            leave_id = leave_request.id
        
        # Approve via API
        headers = self.get_auth_headers(admin_user)
        response = client.post(f'/api/leaves/{leave_id}/approve', headers=headers)
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'approved successfully' in data['message']
    
    def test_reject_leave_request(self, client, admin_user, sample_employee, app):
        """Test rejecting leave request via API."""
        # Create a leave request first
        with app.app_context():
            from hrmsystem.application import db
            from hrmsystem.application.models import LeaveRequest, LeaveStatus
            from datetime import date, timedelta
            
            leave_request = LeaveRequest(
                employee_id=sample_employee.id,
                leave_type_id=1,
                start_date=date.today() + timedelta(days=7),
                end_date=date.today() + timedelta(days=12),
                total_days=5,
                reason='إجازة للاختبار',
                status=LeaveStatus.PENDING
            )
            db.session.add(leave_request)
            db.session.commit()
            leave_id = leave_request.id
        
        # Reject via API
        headers = self.get_auth_headers(admin_user)
        response = client.post(f'/api/leaves/{leave_id}/reject', 
                              json={'rejection_reason': 'عدم توفر بديل'},
                              headers=headers)
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'rejected successfully' in data['message']


class TestAPIStats:
    """Test API statistics endpoints."""
    
    def get_auth_headers(self, user):
        """Helper to get authentication headers."""
        token = generate_token(user.id)
        return {'Authorization': f'Bearer {token}'}
    
    def test_dashboard_stats(self, client, admin_user, sample_employee):
        """Test getting dashboard statistics via API."""
        headers = self.get_auth_headers(admin_user)
        response = client.get('/api/stats/dashboard', headers=headers)
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'stats' in data
        assert 'total_employees' in data['stats']
        assert 'active_employees' in data['stats']
        assert 'pending_leaves' in data['stats']
        assert 'approved_leaves' in data['stats']


class TestAPIPermissions:
    """Test API permission system."""
    
    def get_auth_headers(self, user):
        """Helper to get authentication headers."""
        token = generate_token(user.id)
        return {'Authorization': f'Bearer {token}'}
    
    def test_regular_user_cannot_access_admin_endpoints(self, client, regular_user):
        """Test that regular user cannot access admin-only endpoints."""
        headers = self.get_auth_headers(regular_user)
        
        # Try to create employee (admin only)
        response = client.post('/api/employees', 
                              json={'military_id': '99999', 'name': 'Test'},
                              headers=headers)
        
        assert response.status_code == 403
        data = response.get_json()
        assert 'Insufficient permissions' in data['message']
    
    def test_invalid_token(self, client):
        """Test API access with invalid token."""
        headers = {'Authorization': 'Bearer invalid_token'}
        response = client.get('/api/employees', headers=headers)
        
        assert response.status_code == 401
        data = response.get_json()
        assert 'Invalid token' in data['message']
