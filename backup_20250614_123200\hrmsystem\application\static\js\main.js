// Main JavaScript file for HRM System

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Auto-hide flash messages after 5 seconds
    setTimeout(function() {
        $('.alert-dismissible').alert('close');
    }, 5000);
    
    // Handle leave request form calculations
    if (document.getElementById('leave-form')) {
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');
        const totalDaysSpan = document.getElementById('total-days');
        const employeeSelect = document.getElementById('employee_id');
        const balanceSpan = document.getElementById('leave-balance');
        
        // Calculate total days when dates change
        function calculateDays() {
            if (startDateInput.value && endDateInput.value) {
                fetch(`/leaves/calculate_days?start_date=${startDateInput.value}&end_date=${endDateInput.value}`)
                    .then(response => response.json())
                    .then(data => {
                        totalDaysSpan.textContent = data.days;
                    });
            }
        }
        
        // Get employee leave balance when employee changes
        function getEmployeeBalance() {
            if (employeeSelect.value) {
                fetch(`/leaves/employee_balance/${employeeSelect.value}`)
                    .then(response => response.json())
                    .then(data => {
                        balanceSpan.textContent = data.balance;
                    });
            }
        }
        
        // Add event listeners
        if (startDateInput) startDateInput.addEventListener('change', calculateDays);
        if (endDateInput) endDateInput.addEventListener('change', calculateDays);
        if (employeeSelect) employeeSelect.addEventListener('change', getEmployeeBalance);
        
        // Calculate initial values
        if (startDateInput && startDateInput.value && endDateInput && endDateInput.value) {
            calculateDays();
        }
        
        if (employeeSelect && employeeSelect.value) {
            getEmployeeBalance();
        }
    }
    
    // Handle employee search form
    if (document.getElementById('employee-search-form')) {
        const searchInput = document.getElementById('search');
        const statusSelect = document.getElementById('status');
        const categorySelect = document.getElementById('category');
        const unitInput = document.getElementById('unit');
        
        // Add event listeners for real-time filtering
        searchInput.addEventListener('input', debounce(submitForm, 500));
        statusSelect.addEventListener('change', submitForm);
        categorySelect.addEventListener('change', submitForm);
        unitInput.addEventListener('input', debounce(submitForm, 500));
        
        // Function to submit the form
        function submitForm() {
            document.getElementById('employee-search-form').submit();
        }
        
        // Debounce function to limit how often a function can be called
        function debounce(func, wait) {
            let timeout;
            return function() {
                const context = this;
                const args = arguments;
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    func.apply(context, args);
                }, wait);
            };
        }
    }
    
    // Handle unit autocomplete
    if (document.getElementById('unit')) {
        const unitInput = document.getElementById('unit');
        
        // Fetch units from server
        fetch('/employees/get_units')
            .then(response => response.json())
            .then(data => {
                // Create datalist element
                const datalist = document.createElement('datalist');
                datalist.id = 'unit-list';
                
                // Add options to datalist
                data.forEach(unit => {
                    const option = document.createElement('option');
                    option.value = unit;
                    datalist.appendChild(option);
                });
                
                // Add datalist to document
                document.body.appendChild(datalist);
                
                // Set datalist for input
                unitInput.setAttribute('list', 'unit-list');
            });
    }
    
    // Handle print button
    const printButtons = document.querySelectorAll('.btn-print');
    printButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            window.print();
        });
    });
});
