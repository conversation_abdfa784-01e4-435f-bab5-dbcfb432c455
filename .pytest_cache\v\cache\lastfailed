{"tests/test_security.py": true, "tests/test_auth.py::TestAuth::test_valid_login": true, "tests/test_auth.py::TestAuth::test_invalid_login_wrong_password": true, "tests/test_auth.py::TestAuth::test_invalid_login_wrong_username": true, "tests/test_auth.py::TestAuth::test_logout": true, "tests/test_auth.py::TestUserRegistration::test_register_page_loads": true, "tests/test_auth.py::TestUserRegistration::test_valid_registration": true, "tests/test_auth.py::TestUserRegistration::test_duplicate_username_registration": true, "tests/test_auth.py::TestUserRegistration::test_duplicate_email_registration": true, "tests/test_auth.py::TestUserRegistration::test_password_mismatch_registration": true, "tests/test_auth.py::TestUserPermissions::test_admin_permissions": true, "tests/test_auth.py::TestUserPermissions::test_regular_user_permissions": true, "tests/test_api.py::TestAPIAuth::test_api_login_valid_credentials": true, "tests/test_api.py::TestAPIEmployees::test_get_employees_with_token": true, "tests/test_api.py::TestAPIEmployees::test_get_employees_with_search": true, "tests/test_api.py::TestAPIEmployees::test_get_employees_with_filters": true, "tests/test_api.py::TestAPIEmployees::test_get_employee_by_id": true, "tests/test_api.py::TestAPIEmployees::test_get_nonexistent_employee": true, "tests/test_api.py::TestAPIEmployees::test_create_employee_valid_data": true, "tests/test_api.py::TestAPIEmployees::test_create_employee_missing_data": true, "tests/test_api.py::TestAPIEmployees::test_create_employee_duplicate_military_id": true, "tests/test_api.py::TestAPILeaves::test_get_leaves_with_token": true, "tests/test_api.py::TestAPILeaves::test_approve_leave_request": true, "tests/test_api.py::TestAPILeaves::test_reject_leave_request": true, "tests/test_api.py::TestAPIStats::test_dashboard_stats": true, "tests/test_api.py::TestAPIPermissions::test_regular_user_cannot_access_admin_endpoints": true}