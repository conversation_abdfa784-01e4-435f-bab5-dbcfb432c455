#!/usr/bin/env python3
"""
نسخ احتياطي وإصلاح شامل لنظام إدارة الموارد البشرية
"""
import os
import sys
import shutil
import subprocess
from datetime import datetime
import json

class HRMSystemAnalyzer:
    def __init__(self):
        self.backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.errors_found = []
        self.fixes_applied = []
        self.files_cleaned = []
        self.improvements_made = []
        
    def create_backup(self):
        """إنشاء نسخة احتياطية من النظام"""
        print("🔄 إنشاء نسخة احتياطية...")
        
        try:
            # إنشاء مجلد النسخة الاحتياطية
            os.makedirs(self.backup_dir, exist_ok=True)
            
            # نسخ الملفات المهمة
            important_files = [
                'hrmsystem/',
                'tests/',
                'README.md',
                'requirements.txt',
                'config.py',
                'start_enhanced_hrm.py',
                'run_tests.py'
            ]
            
            for item in important_files:
                if os.path.exists(item):
                    if os.path.isdir(item):
                        shutil.copytree(item, os.path.join(self.backup_dir, item))
                    else:
                        shutil.copy2(item, self.backup_dir)
                    print(f"  ✅ تم نسخ: {item}")
            
            print(f"✅ تم إنشاء النسخة الاحتياطية في: {self.backup_dir}")
            return True
            
        except Exception as e:
            print(f"❌ فشل في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def analyze_and_fix_errors(self):
        """تحليل وإصلاح الأخطاء البرمجية"""
        print("\n🔍 تحليل وإصلاح الأخطاء البرمجية...")
        
        # فحص الأخطاء في الملفات الرئيسية
        python_files = []
        for root, dirs, files in os.walk('hrmsystem'):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        
        # إضافة ملفات أخرى مهمة
        python_files.extend(['start_enhanced_hrm.py', 'run_tests.py', 'fix_database.py'])
        
        for file_path in python_files:
            if os.path.exists(file_path):
                self._check_python_file(file_path)
        
        # إصلاح مشاكل محددة معروفة
        self._fix_known_issues()
        
        return len(self.errors_found)
    
    def _check_python_file(self, file_path):
        """فحص ملف Python للأخطاء"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص الأخطاء النحوية
            try:
                compile(content, file_path, 'exec')
            except SyntaxError as e:
                error = f"خطأ نحوي في {file_path}: {e}"
                self.errors_found.append(error)
                print(f"  ❌ {error}")
            
            # فحص مشاكل الاستيراد الشائعة
            self._check_imports(file_path, content)
            
        except Exception as e:
            error = f"خطأ في قراءة {file_path}: {e}"
            self.errors_found.append(error)
            print(f"  ❌ {error}")
    
    def _check_imports(self, file_path, content):
        """فحص مشاكل الاستيراد"""
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if line.startswith('from ') or line.startswith('import '):
                # فحص الاستيرادات المشكوك فيها
                if 'application.models import Department' in line and 'Department' not in content:
                    error = f"استيراد غير مستخدم في {file_path}:{i} - {line}"
                    self.errors_found.append(error)
    
    def _fix_known_issues(self):
        """إصلاح المشاكل المعروفة"""
        print("  🔧 إصلاح المشاكل المعروفة...")
        
        # إصلاح مشكلة استيراد Department في run.py
        self._fix_run_py_imports()
        
        # إصلاح مشاكل الترميز العربي
        self._fix_encoding_issues()
        
        # إصلاح مشاكل قاعدة البيانات
        self._fix_database_issues()
    
    def _fix_run_py_imports(self):
        """إصلاح مشاكل الاستيراد في run.py"""
        run_py_path = 'hrmsystem/run.py'
        if os.path.exists(run_py_path):
            try:
                with open(run_py_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # التحقق من وجود استيراد Department غير المستخدم
                if 'Department' in content and 'Department(' not in content:
                    # إزالة Department من الاستيراد
                    content = content.replace(', Department', '')
                    content = content.replace('Department, ', '')
                    
                    with open(run_py_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    fix = f"تم إصلاح استيراد Department في {run_py_path}"
                    self.fixes_applied.append(fix)
                    print(f"    ✅ {fix}")
                    
            except Exception as e:
                print(f"    ❌ فشل في إصلاح {run_py_path}: {e}")
    
    def _fix_encoding_issues(self):
        """إصلاح مشاكل الترميز العربي"""
        print("  🔤 فحص وإصلاح مشاكل الترميز العربي...")
        
        # فحص ملفات HTML للتأكد من وجود meta charset
        template_files = []
        for root, dirs, files in os.walk('hrmsystem/application/templates'):
            for file in files:
                if file.endswith('.html'):
                    template_files.append(os.path.join(root, file))
        
        for template_file in template_files:
            self._fix_html_encoding(template_file)
    
    def _fix_html_encoding(self, file_path):
        """إصلاح ترميز ملف HTML"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التحقق من وجود meta charset
            if '<meta charset="UTF-8">' not in content and '<html' in content:
                # إضافة meta charset إذا لم يكن موجوداً
                content = content.replace('<head>', '<head>\n    <meta charset="UTF-8">')
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                fix = f"تم إضافة meta charset إلى {file_path}"
                self.fixes_applied.append(fix)
                print(f"    ✅ {fix}")
                
        except Exception as e:
            print(f"    ❌ فشل في إصلاح ترميز {file_path}: {e}")
    
    def _fix_database_issues(self):
        """إصلاح مشاكل قاعدة البيانات"""
        print("  🗄️ فحص وإصلاح مشاكل قاعدة البيانات...")
        
        # حذف ملفات قاعدة البيانات القديمة أو التالفة
        db_files = [
            'hrmsystem/hrm.db',
            'hrmsystem/app.db',
            'hrm.db',
            'app.db'
        ]
        
        for db_file in db_files:
            if os.path.exists(db_file):
                try:
                    # فحص حجم الملف
                    size = os.path.getsize(db_file)
                    if size < 1024:  # أقل من 1KB - ملف فارغ أو تالف
                        os.remove(db_file)
                        fix = f"تم حذف ملف قاعدة البيانات التالف: {db_file}"
                        self.fixes_applied.append(fix)
                        print(f"    ✅ {fix}")
                except Exception as e:
                    print(f"    ❌ فشل في فحص {db_file}: {e}")
    
    def clean_unnecessary_files(self):
        """تنظيف الملفات غير المطلوبة"""
        print("\n🧹 تنظيف الملفات غير المطلوبة...")
        
        # حذف ملفات __pycache__
        self._clean_pycache()
        
        # حذف ملفات السجلات القديمة
        self._clean_old_logs()
        
        # حذف ملفات مؤقتة
        self._clean_temp_files()
        
        # حذف ملفات النسخ الاحتياطية القديمة
        self._clean_old_backups()
        
        return len(self.files_cleaned)
    
    def _clean_pycache(self):
        """حذف ملفات __pycache__"""
        print("  🗑️ حذف ملفات __pycache__...")
        
        for root, dirs, files in os.walk('.'):
            if '__pycache__' in dirs:
                pycache_path = os.path.join(root, '__pycache__')
                try:
                    shutil.rmtree(pycache_path)
                    self.files_cleaned.append(pycache_path)
                    print(f"    ✅ تم حذف: {pycache_path}")
                except Exception as e:
                    print(f"    ❌ فشل في حذف {pycache_path}: {e}")
    
    def _clean_old_logs(self):
        """حذف ملفات السجلات القديمة"""
        print("  📝 تنظيف ملفات السجلات القديمة...")
        
        log_patterns = ['*.log', '*.log.*', 'logs/*.log']
        for pattern in log_patterns:
            # سيتم تنفيذ هذا لاحقاً
            pass
    
    def _clean_temp_files(self):
        """حذف الملفات المؤقتة"""
        print("  🗂️ حذف الملفات المؤقتة...")
        
        temp_patterns = [
            '*.tmp', '*.temp', '*.bak', '*.swp', '*.swo',
            '.DS_Store', 'Thumbs.db', '*.pyc'
        ]
        
        for root, dirs, files in os.walk('.'):
            for file in files:
                for pattern in temp_patterns:
                    if file.endswith(pattern.replace('*', '')):
                        file_path = os.path.join(root, file)
                        try:
                            os.remove(file_path)
                            self.files_cleaned.append(file_path)
                            print(f"    ✅ تم حذف: {file_path}")
                        except Exception as e:
                            print(f"    ❌ فشل في حذف {file_path}: {e}")
    
    def _clean_old_backups(self):
        """حذف النسخ الاحتياطية القديمة"""
        print("  💾 تنظيف النسخ الاحتياطية القديمة...")
        
        # البحث عن مجلدات النسخ الاحتياطية القديمة
        for item in os.listdir('.'):
            if item.startswith('backup_') and os.path.isdir(item):
                # الاحتفاظ بآخر 3 نسخ احتياطية فقط
                # سيتم تنفيذ هذا لاحقاً
                pass
    
    def generate_report(self):
        """إنشاء تقرير شامل للتحليل والإصلاحات"""
        print("\n📊 إنشاء تقرير شامل...")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'backup_location': self.backup_dir,
            'errors_found': self.errors_found,
            'fixes_applied': self.fixes_applied,
            'files_cleaned': self.files_cleaned,
            'improvements_made': self.improvements_made,
            'summary': {
                'total_errors': len(self.errors_found),
                'total_fixes': len(self.fixes_applied),
                'total_cleaned': len(self.files_cleaned),
                'total_improvements': len(self.improvements_made)
            }
        }
        
        # حفظ التقرير كـ JSON
        report_file = f"system_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # إنشاء تقرير نصي
        text_report = self._generate_text_report(report)
        text_report_file = f"system_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(text_report_file, 'w', encoding='utf-8') as f:
            f.write(text_report)
        
        print(f"✅ تم إنشاء التقرير: {report_file}")
        print(f"✅ تم إنشاء التقرير النصي: {text_report_file}")
        
        return report_file, text_report_file
    
    def _generate_text_report(self, report):
        """إنشاء تقرير نصي"""
        text = f"""
📋 تقرير تحليل وإصلاح نظام إدارة الموارد البشرية
═══════════════════════════════════════════════════════════

📅 التاريخ والوقت: {report['timestamp']}
💾 موقع النسخة الاحتياطية: {report['backup_location']}

📊 ملخص النتائج:
═══════════════════════════════════════════════════════════
• إجمالي الأخطاء المكتشفة: {report['summary']['total_errors']}
• إجمالي الإصلاحات المطبقة: {report['summary']['total_fixes']}
• إجمالي الملفات المنظفة: {report['summary']['total_cleaned']}
• إجمالي التحسينات: {report['summary']['total_improvements']}

"""
        
        if report['errors_found']:
            text += "\n❌ الأخطاء المكتشفة:\n"
            text += "═" * 50 + "\n"
            for i, error in enumerate(report['errors_found'], 1):
                text += f"{i}. {error}\n"
        
        if report['fixes_applied']:
            text += "\n✅ الإصلاحات المطبقة:\n"
            text += "═" * 50 + "\n"
            for i, fix in enumerate(report['fixes_applied'], 1):
                text += f"{i}. {fix}\n"
        
        if report['files_cleaned']:
            text += "\n🧹 الملفات المنظفة:\n"
            text += "═" * 50 + "\n"
            for i, file in enumerate(report['files_cleaned'], 1):
                text += f"{i}. {file}\n"
        
        text += "\n🎯 التوصيات:\n"
        text += "═" * 50 + "\n"
        text += "• تشغيل الاختبارات للتأكد من عمل النظام\n"
        text += "• مراجعة التقارير المطبوعة\n"
        text += "• اختبار جميع وظائف النظام\n"
        text += "• إنشاء نسخة احتياطية دورية\n"
        
        return text

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة التحليل والإصلاح الشامل لنظام إدارة الموارد البشرية")
    print("=" * 80)
    
    analyzer = HRMSystemAnalyzer()
    
    # إنشاء نسخة احتياطية
    if not analyzer.create_backup():
        print("❌ فشل في إنشاء النسخة الاحتياطية. التوقف.")
        return
    
    # تحليل وإصلاح الأخطاء
    errors_count = analyzer.analyze_and_fix_errors()
    
    # تنظيف الملفات
    cleaned_count = analyzer.clean_unnecessary_files()
    
    # إنشاء التقرير
    report_file, text_report_file = analyzer.generate_report()
    
    # عرض النتائج النهائية
    print("\n" + "=" * 80)
    print("🎉 تم الانتهاء من التحليل والإصلاح!")
    print("=" * 80)
    print(f"📊 النتائج:")
    print(f"   • الأخطاء المكتشفة: {errors_count}")
    print(f"   • الإصلاحات المطبقة: {len(analyzer.fixes_applied)}")
    print(f"   • الملفات المنظفة: {cleaned_count}")
    print(f"   • التقارير: {report_file}, {text_report_file}")
    
    print(f"\n📁 النسخة الاحتياطية: {analyzer.backup_dir}")
    print(f"📋 التقرير النصي: {text_report_file}")

if __name__ == '__main__':
    main()
