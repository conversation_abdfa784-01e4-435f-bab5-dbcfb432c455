#!/usr/bin/env python3
"""
إنشاء ملف Excel نموذج لاستيراد الموظفين
"""
import csv
import os

def create_excel_template():
    """إنشاء ملف Excel نموذج"""
    
    # إنشاء مجلد templates إذا لم يكن موجوداً
    os.makedirs('templates', exist_ok=True)
    
    # البيانات النموذجية
    headers = [
        'الاسم', 'الرقم العسكري', 'الرقم الوطني', 'الرتبة', 'الوحدة', 'العمل المكلف به',
        'الفئة', 'الحالة', 'فصيلة الدم', 'تاريخ الميلاد', 'مكان الميلاد', 'السكن الحالي',
        'المؤهل العلمي', 'تاريخ المؤهل', 'اسم البنك', 'رقم الحساب', 'رقم الهاتف',
        'البريد الإلكتروني', 'رصيد الإجازات', 'تاريخ التعيين', 'تاريخ آخر ترقية', 'ملاحظات الحالة'
    ]
    
    sample_data = [
        [
            'أحمد محمد علي السالم', '12345', '1234567890', 'ملازم', 'الكتيبة الأولى', 'ضابط عمليات',
            'ضباط', 'مستمر', 'O+', '1990-05-15', 'الرياض', 'الرياض - حي النرجس',
            'بكالوريوس هندسة', '2012-06-20', 'البنك الأهلي السعودي', '123456789', '0501234567',
            '<EMAIL>', '30', '2015-01-01', '2020-01-01', ''
        ],
        [
            'محمد أحمد الخالد', '67890', '9876543210', 'نقيب', 'الكتيبة الثانية', 'ضابط إداري',
            'ضباط', 'مستمر', 'A+', '1988-03-10', 'جدة', 'جدة - حي الصفا',
            'بكالوريوس إدارة أعمال', '2010-05-15', 'بنك الراجحي', '987654321', '0509876543',
            '<EMAIL>', '25', '2012-03-01', '2018-06-01', ''
        ],
        [
            'سالم عبدالله المطيري', '11111', '1111111111', 'رقيب أول', 'القيادة العامة', 'رقيب إداري',
            'ضباط صف', 'مستمر', 'B+', '1985-12-25', 'الدمام', 'الدمام - حي الشاطئ',
            'ثانوية عامة', '2003-06-01', 'بنك سامبا', '555666777', '0501111111',
            '<EMAIL>', '20', '2008-09-01', '2016-01-01', ''
        ],
        [
            'خالد سعد الأحمد', '22222', '2222222222', 'عريف', 'الكتيبة الثالثة', 'عريف أمن',
            'ضباط صف', 'مستمر', 'AB+', '1992-08-20', 'مكة المكرمة', 'مكة - حي العزيزية',
            'دبلوم فني', '2010-07-15', 'بنك الإنماء', '444555666', '0502222222',
            '<EMAIL>', '22', '2013-05-01', '2019-03-01', ''
        ],
        [
            'عبدالرحمن محمد القحطاني', '33333', '1333333333', 'مقدم', 'القيادة العامة', 'ضابط تخطيط',
            'ضباط', 'مستمر', 'O-', '1980-11-05', 'أبها', 'أبها - حي المنهل',
            'ماجستير إدارة عامة', '2005-12-10', 'البنك السعودي للاستثمار', '777888999', '0503333333',
            '<EMAIL>', '35', '2008-02-01', '2015-08-01', 'ضابط متميز'
        ]
    ]
    
    # إنشاء ملف CSV
    csv_file = 'templates/نموذج_استيراد_الموظفين.csv'
    with open(csv_file, 'w', newline='', encoding='utf-8-sig') as file:
        writer = csv.writer(file)
        writer.writerow(headers)
        writer.writerows(sample_data)
    
    print(f"✅ تم إنشاء ملف CSV: {csv_file}")
    
    # إنشاء ملف فارغ للاستيراد
    empty_csv_file = 'templates/نموذج_فارغ_لاستيراد_الموظفين.csv'
    with open(empty_csv_file, 'w', newline='', encoding='utf-8-sig') as file:
        writer = csv.writer(file)
        writer.writerow(headers)
        # إضافة صف فارغ واحد كمثال
        writer.writerow([''] * len(headers))
    
    print(f"✅ تم إنشاء ملف فارغ: {empty_csv_file}")
    
    # إنشاء ملف HTML للعرض
    html_file = 'templates/نموذج_استيراد_الموظفين.html'
    with open(html_file, 'w', encoding='utf-8') as file:
        file.write('''<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نموذج استيراد الموظفين</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .required { background-color: #ffe6e6; }
        .optional { background-color: #e6f3ff; }
        .instructions { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .download-links { margin: 20px 0; }
        .download-links a { display: inline-block; margin: 5px; padding: 10px 15px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .download-links a:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <h1>📋 نموذج استيراد بيانات الموظفين</h1>
    
    <div class="instructions">
        <h2>🎯 تعليمات الاستخدام:</h2>
        <ul>
            <li><strong>الأعمدة الحمراء:</strong> مطلوبة ويجب ملؤها</li>
            <li><strong>الأعمدة الزرقاء:</strong> اختيارية</li>
            <li><strong>التواريخ:</strong> يجب أن تكون بتنسيق YYYY-MM-DD</li>
            <li><strong>الرقم العسكري:</strong> يجب أن يكون فريد لكل موظف</li>
        </ul>
    </div>
    
    <div class="download-links">
        <h3>📥 تحميل الملفات:</h3>
        <a href="نموذج_استيراد_الموظفين.csv">تحميل النموذج مع البيانات</a>
        <a href="نموذج_فارغ_لاستيراد_الموظفين.csv">تحميل النموذج الفارغ</a>
        <a href="تعليمات_استيراد_الموظفين.txt">تحميل التعليمات</a>
    </div>
    
    <h2>📊 جدول البيانات النموذجية:</h2>
    <table>
        <thead>
            <tr>''')
        
        # إضافة رؤوس الأعمدة
        required_columns = ['الاسم', 'الرقم العسكري', 'الوحدة', 'العمل المكلف به']
        for header in headers:
            css_class = 'required' if header in required_columns else 'optional'
            file.write(f'<th class="{css_class}">{header}</th>')
        
        file.write('''
            </tr>
        </thead>
        <tbody>''')
        
        # إضافة البيانات النموذجية
        for row in sample_data:
            file.write('<tr>')
            for cell in row:
                file.write(f'<td>{cell}</td>')
            file.write('</tr>')
        
        file.write('''
        </tbody>
    </table>
    
    <div class="instructions">
        <h3>📝 ملاحظات مهمة:</h3>
        <ul>
            <li>احفظ الملف بصيغة CSV مع ترميز UTF-8</li>
            <li>لا تغير أسماء الأعمدة</li>
            <li>تأكد من عدم وجود فواصل إضافية في البيانات</li>
            <li>راجع البيانات قبل الرفع</li>
        </ul>
    </div>
    
    <footer style="margin-top: 40px; text-align: center; color: #666;">
        <p>نظام إدارة الموارد البشرية - الإصدار 2.0</p>
    </footer>
</body>
</html>''')
    
    print(f"✅ تم إنشاء ملف HTML: {html_file}")
    
    return csv_file, empty_csv_file, html_file

if __name__ == '__main__':
    print("🔄 إنشاء ملفات نموذج استيراد الموظفين...")
    print("=" * 60)
    
    try:
        csv_file, empty_file, html_file = create_excel_template()
        
        print("\n🎉 تم إنشاء جميع الملفات بنجاح!")
        print(f"\n📁 الملفات المنشأة:")
        print(f"   📊 {csv_file}")
        print(f"   📄 {empty_file}")
        print(f"   🌐 {html_file}")
        print(f"   📝 templates/تعليمات_استيراد_الموظفين.txt")
        
        print(f"\n🔗 يمكنك الآن:")
        print(f"   • استخدام الملفات في صفحة الاستيراد")
        print(f"   • فتح الملف HTML في المتصفح للمراجعة")
        print(f"   • تحميل الملفات من مجلد templates")
        
    except Exception as e:
        print(f"\n❌ حدث خطأ: {e}")
        import traceback
        traceback.print_exc()
