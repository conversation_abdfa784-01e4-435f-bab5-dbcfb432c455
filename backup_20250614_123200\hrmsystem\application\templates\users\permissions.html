{% extends 'layouts/base.html' %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-user-shield"></i> إدارة صلاحيات المستخدم</h2>
        <a href="{{ url_for('users.view', id=user.id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة إلى بيانات المستخدم
        </a>
    </div>
    
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-key"></i> صلاحيات المستخدم: {{ user.username }}</h5>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        {% if user.profile_image %}
                        <img src="{{ url_for('static', filename='img/users/' + user.profile_image) }}" alt="{{ user.username }}" class="img-thumbnail rounded-circle me-3" style="width: 80px; height: 80px; object-fit: cover;">
                        {% else %}
                        <img src="{{ url_for('static', filename='img/default_profile.png') }}" alt="{{ user.username }}" class="img-thumbnail rounded-circle me-3" style="width: 80px; height: 80px; object-fit: cover;">
                        {% endif %}
                        <div>
                            <h4>{{ user.full_name or user.username }}</h4>
                            <p class="text-muted mb-0">{{ user.email }}</p>
                            <div class="mt-1">
                                {% if user.role.name == 'ADMIN' %}
                                <span class="badge bg-danger">{{ user.role.value }}</span>
                                {% elif user.role.name == 'SUPERVISOR' %}
                                <span class="badge bg-warning">{{ user.role.value }}</span>
                                {% else %}
                                <span class="badge bg-info">{{ user.role.value }}</span>
                                {% endif %}
                                
                                {% if user.status.name == 'ACTIVE' %}
                                <span class="badge bg-success">{{ user.status.value }}</span>
                                {% elif user.status.name == 'INACTIVE' %}
                                <span class="badge bg-secondary">{{ user.status.value }}</span>
                                {% else %}
                                <span class="badge bg-danger">{{ user.status.value }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-info">
                        <h5 class="alert-heading"><i class="fas fa-info-circle"></i> ملاحظات</h5>
                        <ul class="mb-0">
                            <li>مدير النظام لديه جميع الصلاحيات تلقائيًا.</li>
                            <li>المشرف لديه صلاحيات محددة افتراضية.</li>
                            <li>يمكنك تخصيص صلاحيات المستخدمين العاديين حسب الحاجة.</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <form method="POST" action="{{ url_for('users.manage_permissions', id=user.id) }}">
                {{ form.hidden_tag() }}
                
                <div class="row">
                    <!-- صلاحيات الموظفين -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-users"></i> صلاحيات الموظفين</h5>
                            </div>
                            <div class="card-body">
                                <div class="permissions-group">
                                    {% for subfield in form.employee_permissions %}
                                    <div class="form-check mb-2">
                                        {{ subfield(class="form-check-input") }}
                                        {{ subfield.label(class="form-check-label") }}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- صلاحيات الإجازات -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="fas fa-calendar-alt"></i> صلاحيات الإجازات</h5>
                            </div>
                            <div class="card-body">
                                <div class="permissions-group">
                                    {% for subfield in form.leave_permissions %}
                                    <div class="form-check mb-2">
                                        {{ subfield(class="form-check-input") }}
                                        {{ subfield.label(class="form-check-label") }}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- صلاحيات المستخدمين -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="fas fa-user-cog"></i> صلاحيات المستخدمين</h5>
                            </div>
                            <div class="card-body">
                                <div class="permissions-group">
                                    {% for subfield in form.user_permissions %}
                                    <div class="form-check mb-2">
                                        {{ subfield(class="form-check-input") }}
                                        {{ subfield.label(class="form-check-label") }}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- صلاحيات التقارير -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0"><i class="fas fa-chart-bar"></i> صلاحيات التقارير</h5>
                            </div>
                            <div class="card-body">
                                <div class="permissions-group">
                                    {% for subfield in form.report_permissions %}
                                    <div class="form-check mb-2">
                                        {{ subfield(class="form-check-input") }}
                                        {{ subfield.label(class="form-check-label") }}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- صلاحيات النظام -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-danger text-white">
                                <h5 class="mb-0"><i class="fas fa-cogs"></i> صلاحيات النظام</h5>
                            </div>
                            <div class="card-body">
                                <div class="permissions-group">
                                    {% for subfield in form.system_permissions %}
                                    <div class="form-check mb-2">
                                        {{ subfield(class="form-check-input") }}
                                        {{ subfield.label(class="form-check-label") }}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{{ url_for('users.view', id=user.id) }}" class="btn btn-secondary me-md-2">إلغاء</a>
                    {{ form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    .permissions-group {
        max-height: 300px;
        overflow-y: auto;
    }
</style>
{% endblock %}
