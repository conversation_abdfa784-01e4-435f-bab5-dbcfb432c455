<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} | نظام إدارة الموارد البشرية</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">

    {% block styles %}{% endblock %}
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('dashboard.index') }}">
                <img src="{{ url_for('static', filename='img/hrm_logo.svg') }}" alt="Logo" width="50" height="50" class="d-inline-block align-text-top me-2">
                <span>نظام إدارة الموارد البشرية</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                {% if current_user.is_authenticated %}
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'dashboard.index' %}active{% endif %}" href="{{ url_for('dashboard.index') }}">
                            <i class="fas fa-tachometer-alt me-1"></i> لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint.startswith('employees.') %}active{% endif %}" href="{{ url_for('employees.index') }}">
                            <i class="fas fa-users me-1"></i> الموظفين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint.startswith('leaves.') and request.endpoint != 'leaves.types' %}active{% endif %}" href="{{ url_for('leaves.index') }}">
                            <i class="fas fa-calendar-alt me-1"></i> الإجازات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'leaves.types' %}active{% endif %}" href="{{ url_for('leaves.types') }}">
                            <i class="fas fa-list-alt me-1"></i> أنواع الإجازات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'leaves.calendar' %}active{% endif %}" href="{{ url_for('leaves.calendar') }}">
                            <i class="fas fa-calendar me-1"></i> التقويم
                        </a>
                    </li>
                    {% if current_user.is_admin or current_user.role == 'admin' or (current_user.role is defined and current_user.role.name == 'ADMIN') %}
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint.startswith('users.') %}active{% endif %}" href="{{ url_for('users.index') }}">
                            <i class="fas fa-user-shield me-1"></i> المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'users.audit_logs' %}active{% endif %}" href="{{ url_for('users.audit_logs') }}">
                            <i class="fas fa-history me-1"></i> سجل التغييرات
                        </a>
                    </li>
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> {{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('users.profile') }}">
                                    <i class="fas fa-user-circle me-1"></i> الملف الشخصي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('users.change_password') }}">
                                    <i class="fas fa-key me-1"></i> تغيير كلمة المرور
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                    <i class="fas fa-sign-out-alt me-1"></i> تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container py-4">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <img src="{{ url_for('static', filename='img/hrm_logo.svg') }}" alt="Logo" width="40" height="40" class="mb-2">
            <p class="mb-0">&copy; {{ now.year }} نظام إدارة الموارد البشرية. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    {% block scripts %}{% endblock %}
</body>
</html>
