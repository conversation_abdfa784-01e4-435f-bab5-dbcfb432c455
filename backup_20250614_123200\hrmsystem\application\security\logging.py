"""
Advanced logging system for HRM System
"""
import logging
import os
from datetime import datetime
from flask import request, current_app, g
from flask_login import current_user
import json
from functools import wraps


class SecurityLogger:
    """Enhanced security logging."""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize logging with Flask app."""
        # Create logs directory if it doesn't exist
        log_dir = os.path.join(app.root_path, '..', 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        # Configure security logger
        security_logger = logging.getLogger('hrm_security')
        security_logger.setLevel(logging.INFO)
        
        # Create file handler for security events
        security_handler = logging.FileHandler(
            os.path.join(log_dir, 'security.log'),
            encoding='utf-8'
        )
        security_handler.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        security_handler.setFormatter(formatter)
        
        # Add handler to logger
        if not security_logger.handlers:
            security_logger.addHandler(security_handler)
        
        # Configure audit logger
        audit_logger = logging.getLogger('hrm_audit')
        audit_logger.setLevel(logging.INFO)
        
        # Create file handler for audit events
        audit_handler = logging.FileHandler(
            os.path.join(log_dir, 'audit.log'),
            encoding='utf-8'
        )
        audit_handler.setLevel(logging.INFO)
        audit_handler.setFormatter(formatter)
        
        if not audit_logger.handlers:
            audit_logger.addHandler(audit_handler)
        
        # Configure error logger
        error_logger = logging.getLogger('hrm_errors')
        error_logger.setLevel(logging.ERROR)
        
        # Create file handler for errors
        error_handler = logging.FileHandler(
            os.path.join(log_dir, 'errors.log'),
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        
        if not error_logger.handlers:
            error_logger.addHandler(error_handler)


def get_client_info():
    """Get client information for logging."""
    return {
        'ip_address': request.remote_addr,
        'user_agent': request.headers.get('User-Agent', ''),
        'method': request.method,
        'url': request.url,
        'endpoint': request.endpoint
    }


def log_security_event(event_type, message, level='INFO', **kwargs):
    """Log security events."""
    logger = logging.getLogger('hrm_security')
    
    # Get user info
    user_info = {}
    if current_user.is_authenticated:
        user_info = {
            'user_id': current_user.id,
            'username': current_user.username,
            'role': current_user.role.value if current_user.role else None
        }
    
    # Combine all information
    log_data = {
        'event_type': event_type,
        'message': message,
        'timestamp': datetime.now().isoformat(),
        'client_info': get_client_info(),
        'user_info': user_info,
        **kwargs
    }
    
    log_message = json.dumps(log_data, ensure_ascii=False, default=str)
    
    if level == 'ERROR':
        logger.error(log_message)
    elif level == 'WARNING':
        logger.warning(log_message)
    else:
        logger.info(log_message)


def log_audit_event(action, entity, entity_id=None, old_values=None, new_values=None):
    """Log audit events."""
    logger = logging.getLogger('hrm_audit')
    
    # Get user info
    user_info = {}
    if current_user.is_authenticated:
        user_info = {
            'user_id': current_user.id,
            'username': current_user.username
        }
    
    log_data = {
        'action': action,
        'entity': entity,
        'entity_id': entity_id,
        'old_values': old_values,
        'new_values': new_values,
        'timestamp': datetime.now().isoformat(),
        'client_info': get_client_info(),
        'user_info': user_info
    }
    
    log_message = json.dumps(log_data, ensure_ascii=False, default=str)
    logger.info(log_message)


def log_error(error, context=None):
    """Log application errors."""
    logger = logging.getLogger('hrm_errors')
    
    error_data = {
        'error_type': type(error).__name__,
        'error_message': str(error),
        'context': context,
        'timestamp': datetime.now().isoformat(),
        'client_info': get_client_info()
    }
    
    if current_user.is_authenticated:
        error_data['user_info'] = {
            'user_id': current_user.id,
            'username': current_user.username
        }
    
    log_message = json.dumps(error_data, ensure_ascii=False, default=str)
    logger.error(log_message)


def log_login_attempt(username, success, failure_reason=None):
    """Log login attempts."""
    event_type = 'LOGIN_SUCCESS' if success else 'LOGIN_FAILURE'
    message = f"Login attempt for user: {username}"
    
    extra_data = {'username': username}
    if not success and failure_reason:
        extra_data['failure_reason'] = failure_reason
    
    level = 'INFO' if success else 'WARNING'
    log_security_event(event_type, message, level, **extra_data)


def log_permission_denied(permission, resource=None):
    """Log permission denied events."""
    message = f"Permission denied: {permission}"
    if resource:
        message += f" for resource: {resource}"
    
    log_security_event('PERMISSION_DENIED', message, 'WARNING', 
                      permission=permission, resource=resource)


def audit_log(action, entity):
    """Decorator to automatically log CRUD operations."""
    def decorator(f):
        @wraps(f)
        def decorated(*args, **kwargs):
            # Store original values for update operations
            old_values = None
            if action == 'UPDATE' and 'id' in request.view_args:
                entity_id = request.view_args['id']
                # You would need to implement logic to get old values
                # This is a simplified version
                old_values = {'id': entity_id}
            
            # Execute the function
            result = f(*args, **kwargs)
            
            # Log the action
            entity_id = None
            new_values = None
            
            if hasattr(result, 'id'):
                entity_id = result.id
            elif 'id' in request.view_args:
                entity_id = request.view_args['id']
            
            if request.is_json:
                new_values = request.get_json()
            elif request.form:
                new_values = dict(request.form)
            
            log_audit_event(action, entity, entity_id, old_values, new_values)
            
            return result
        return decorated
    return decorator


def security_monitor(f):
    """Decorator to monitor security-sensitive operations."""
    @wraps(f)
    def decorated(*args, **kwargs):
        start_time = datetime.now()
        
        try:
            result = f(*args, **kwargs)
            
            # Log successful operation
            duration = (datetime.now() - start_time).total_seconds()
            log_security_event(
                'OPERATION_SUCCESS',
                f"Function {f.__name__} executed successfully",
                duration=duration
            )
            
            return result
            
        except Exception as e:
            # Log failed operation
            duration = (datetime.now() - start_time).total_seconds()
            log_security_event(
                'OPERATION_FAILURE',
                f"Function {f.__name__} failed: {str(e)}",
                'ERROR',
                duration=duration,
                error_type=type(e).__name__
            )
            
            # Re-raise the exception
            raise
    
    return decorated


# Initialize the security logger
security_logger = SecurityLogger()
