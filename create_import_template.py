#!/usr/bin/env python3
"""
إنشاء ملف نموذج Excel لاستيراد بيانات الموظفين
"""
import pandas as pd
from datetime import date, timedelta
import os

def create_employee_import_template():
    """إنشاء ملف نموذج Excel لاستيراد الموظفين"""
    
    # تحديد الأعمدة المطلوبة والاختيارية
    columns = [
        'الاسم',                    # مطلوب
        'الرقم العسكري',            # مطلوب
        'الرقم الوطني',             # اختياري
        'الرتبة',                   # اختياري
        'الوحدة',                   # مطلوب
        'العمل المكلف به',          # مطلوب
        'الفئة',                    # اختياري (ضباط، ضباط صف، موظف)
        'الحالة',                   # اختياري (مستمر، غائب/هارب، منتدب، في إجازة، موقوف، متفرق، عيادة طبية)
        'فصيلة الدم',               # اختياري
        'تاريخ الميلاد',            # اختياري (YYYY-MM-DD)
        'مكان الميلاد',             # اختياري
        'السكن الحالي',             # اختياري
        'المؤهل العلمي',            # اختياري
        'تاريخ المؤهل',             # اختياري (YYYY-MM-DD)
        'اسم البنك',                # اختياري
        'رقم الحساب',               # اختياري
        'رقم الهاتف',               # اختياري
        'البريد الإلكتروني',        # اختياري
        'رصيد الإجازات',            # اختياري (رقم)
        'تاريخ التعيين',            # اختياري (YYYY-MM-DD)
        'تاريخ آخر ترقية',          # اختياري (YYYY-MM-DD)
        'ملاحظات الحالة'            # اختياري
    ]
    
    # إنشاء بيانات نموذجية
    sample_data = [
        {
            'الاسم': 'أحمد محمد علي السالم',
            'الرقم العسكري': '12345',
            'الرقم الوطني': '1234567890',
            'الرتبة': 'ملازم',
            'الوحدة': 'الكتيبة الأولى',
            'العمل المكلف به': 'ضابط عمليات',
            'الفئة': 'ضباط',
            'الحالة': 'مستمر',
            'فصيلة الدم': 'O+',
            'تاريخ الميلاد': '1990-05-15',
            'مكان الميلاد': 'الرياض',
            'السكن الحالي': 'الرياض - حي النرجس',
            'المؤهل العلمي': 'بكالوريوس هندسة',
            'تاريخ المؤهل': '2012-06-20',
            'اسم البنك': 'البنك الأهلي السعودي',
            'رقم الحساب': '123456789',
            'رقم الهاتف': '0501234567',
            'البريد الإلكتروني': '<EMAIL>',
            'رصيد الإجازات': '30',
            'تاريخ التعيين': '2015-01-01',
            'تاريخ آخر ترقية': '2020-01-01',
            'ملاحظات الحالة': ''
        },
        {
            'الاسم': 'محمد أحمد الخالد',
            'الرقم العسكري': '67890',
            'الرقم الوطني': '9876543210',
            'الرتبة': 'نقيب',
            'الوحدة': 'الكتيبة الثانية',
            'العمل المكلف به': 'ضابط إداري',
            'الفئة': 'ضباط',
            'الحالة': 'مستمر',
            'فصيلة الدم': 'A+',
            'تاريخ الميلاد': '1988-03-10',
            'مكان الميلاد': 'جدة',
            'السكن الحالي': 'جدة - حي الصفا',
            'المؤهل العلمي': 'بكالوريوس إدارة أعمال',
            'تاريخ المؤهل': '2010-05-15',
            'اسم البنك': 'بنك الراجحي',
            'رقم الحساب': '987654321',
            'رقم الهاتف': '0509876543',
            'البريد الإلكتروني': '<EMAIL>',
            'رصيد الإجازات': '25',
            'تاريخ التعيين': '2012-03-01',
            'تاريخ آخر ترقية': '2018-06-01',
            'ملاحظات الحالة': ''
        },
        {
            'الاسم': 'سالم عبدالله المطيري',
            'الرقم العسكري': '11111',
            'الرقم الوطني': '1111111111',
            'الرتبة': 'رقيب أول',
            'الوحدة': 'القيادة العامة',
            'العمل المكلف به': 'رقيب إداري',
            'الفئة': 'ضباط صف',
            'الحالة': 'مستمر',
            'فصيلة الدم': 'B+',
            'تاريخ الميلاد': '1985-12-25',
            'مكان الميلاد': 'الدمام',
            'السكن الحالي': 'الدمام - حي الشاطئ',
            'المؤهل العلمي': 'ثانوية عامة',
            'تاريخ المؤهل': '2003-06-01',
            'اسم البنك': 'بنك سامبا',
            'رقم الحساب': '555666777',
            'رقم الهاتف': '0501111111',
            'البريد الإلكتروني': '<EMAIL>',
            'رصيد الإجازات': '20',
            'تاريخ التعيين': '2008-09-01',
            'تاريخ آخر ترقية': '2016-01-01',
            'ملاحظات الحالة': ''
        }
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame(sample_data, columns=columns)
    
    # إنشاء مجلد للملفات إذا لم يكن موجوداً
    output_dir = 'templates'
    os.makedirs(output_dir, exist_ok=True)
    
    # حفظ الملف Excel
    output_file_xlsx = os.path.join(output_dir, 'نموذج_استيراد_الموظفين.xlsx')
    output_file_csv = os.path.join(output_dir, 'نموذج_استيراد_الموظفين.csv')

    try:
        # محاولة إنشاء ملف Excel
        with pd.ExcelWriter(output_file_xlsx, engine='openpyxl') as writer:
            # كتابة البيانات النموذجية
            df.to_excel(writer, sheet_name='بيانات الموظفين', index=False)

            # إنشاء ورقة التعليمات
            instructions_data = {
                'العمود': [
                    'الاسم', 'الرقم العسكري', 'الرقم الوطني', 'الرتبة', 'الوحدة', 'العمل المكلف به',
                    'الفئة', 'الحالة', 'فصيلة الدم', 'تاريخ الميلاد', 'مكان الميلاد', 'السكن الحالي',
                    'المؤهل العلمي', 'تاريخ المؤهل', 'اسم البنك', 'رقم الحساب', 'رقم الهاتف',
                    'البريد الإلكتروني', 'رصيد الإجازات', 'تاريخ التعيين', 'تاريخ آخر ترقية', 'ملاحظات الحالة'
                ],
                'مطلوب/اختياري': [
                    'مطلوب', 'مطلوب', 'اختياري', 'اختياري', 'مطلوب', 'مطلوب',
                    'اختياري', 'اختياري', 'اختياري', 'اختياري', 'اختياري', 'اختياري',
                    'اختياري', 'اختياري', 'اختياري', 'اختياري', 'اختياري',
                    'اختياري', 'اختياري', 'اختياري', 'اختياري', 'اختياري'
                ],
                'التنسيق/القيم المسموحة': [
                    'نص - الاسم الكامل', 'نص/رقم - يجب أن يكون فريد', 'رقم - 10 أرقام',
                    'نص - مثل: ملازم، نقيب، رقيب', 'نص - اسم الوحدة', 'نص - المنصب أو العمل',
                    'ضباط، ضباط صف، موظف', 'مستمر، غائب/هارب، منتدب، في إجازة، موقوف، متفرق، عيادة طبية',
                    'A+، A-، B+، B-، AB+، AB-، O+، O-', 'YYYY-MM-DD مثل: 1990-05-15',
                    'نص - مكان الولادة', 'نص - العنوان الحالي', 'نص - المؤهل التعليمي',
                    'YYYY-MM-DD مثل: 2012-06-20', 'نص - اسم البنك', 'نص/رقم - رقم الحساب البنكي',
                    'رقم - مثل: 0501234567', 'بريد إلكتروني صحيح', 'رقم - عدد أيام الإجازة المتبقية',
                    'YYYY-MM-DD مثل: 2015-01-01', 'YYYY-MM-DD مثل: 2020-01-01', 'نص - ملاحظات إضافية'
                ]
            }

            instructions_df = pd.DataFrame(instructions_data)
            instructions_df.to_excel(writer, sheet_name='التعليمات', index=False)

        print(f"✅ تم إنشاء ملف Excel: {output_file_xlsx}")
        output_file = output_file_xlsx

    except ImportError:
        # إذا لم تكن openpyxl متوفرة، أنشئ ملف CSV
        df.to_csv(output_file_csv, index=False, encoding='utf-8-sig')

        # إنشاء ملف تعليمات منفصل
        instructions_file = os.path.join(output_dir, 'تعليمات_الاستيراد.txt')
        with open(instructions_file, 'w', encoding='utf-8') as f:
            f.write("تعليمات استيراد بيانات الموظفين\n")
            f.write("=" * 50 + "\n\n")
            f.write("الأعمدة المطلوبة:\n")
            f.write("• الاسم (مطلوب)\n")
            f.write("• الرقم العسكري (مطلوب)\n")
            f.write("• الوحدة (مطلوب)\n")
            f.write("• العمل المكلف به (مطلوب)\n\n")
            f.write("الأعمدة الاختيارية:\n")
            f.write("• الرقم الوطني، الرتبة، الفئة، الحالة، فصيلة الدم\n")
            f.write("• تاريخ الميلاد، مكان الميلاد، السكن الحالي\n")
            f.write("• المؤهل العلمي، تاريخ المؤهل، اسم البنك، رقم الحساب\n")
            f.write("• رقم الهاتف، البريد الإلكتروني، رصيد الإجازات\n")
            f.write("• تاريخ التعيين، تاريخ آخر ترقية، ملاحظات الحالة\n\n")
            f.write("ملاحظات:\n")
            f.write("• التواريخ بتنسيق: YYYY-MM-DD\n")
            f.write("• فصائل الدم: A+, A-, B+, B-, AB+, AB-, O+, O-\n")
            f.write("• الفئات: ضباط، ضباط صف، موظف\n")

        print(f"✅ تم إنشاء ملف CSV: {output_file_csv}")
        print(f"✅ تم إنشاء ملف التعليمات: {instructions_file}")
        output_file = output_file_csv
    
    print(f"✅ تم إنشاء ملف النموذج: {output_file}")
    print("\n📋 معلومات الملف:")
    print(f"   📁 المسار: {os.path.abspath(output_file)}")
    print(f"   📊 عدد الأعمدة: {len(columns)}")
    print(f"   📝 عدد الأمثلة: {len(sample_data)}")
    print("\n🔍 الأعمدة المطلوبة:")
    required_columns = ['الاسم', 'الرقم العسكري', 'الوحدة', 'العمل المكلف به']
    for col in required_columns:
        print(f"   ✅ {col}")
    
    print("\n📌 ملاحظات مهمة:")
    print("   • يجب أن تكون الأعمدة المطلوبة موجودة ومملوءة")
    print("   • الرقم العسكري يجب أن يكون فريد لكل موظف")
    print("   • التواريخ يجب أن تكون بتنسيق YYYY-MM-DD")
    print("   • فصائل الدم المسموحة: A+, A-, B+, B-, AB+, AB-, O+, O-")
    print("   • الفئات المسموحة: ضباط، ضباط صف، موظف")
    
    return output_file

if __name__ == '__main__':
    print("🔄 إنشاء ملف نموذج استيراد الموظفين...")
    print("=" * 60)
    
    try:
        template_file = create_employee_import_template()
        print("\n🎉 تم إنشاء الملف بنجاح!")
        print(f"\nيمكنك الآن استخدام الملف: {template_file}")
        print("لاستيراد بيانات الموظفين في النظام.")
        
    except Exception as e:
        print(f"\n❌ حدث خطأ أثناء إنشاء الملف: {e}")
        import traceback
        traceback.print_exc()
