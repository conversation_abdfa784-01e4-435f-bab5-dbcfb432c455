{% extends 'layouts/base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-users-cog"></i> إدارة المستخدمين</h2>
        <div>
            <a href="{{ url_for('users.create') }}" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> إضافة مستخدم جديد
            </a>
            <a href="{{ url_for('users.audit_logs') }}" class="btn btn-info">
                <i class="fas fa-history"></i> سجل التغييرات
            </a>
        </div>
    </div>

    <!-- Search Form -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-search"></i> بحث متقدم</h5>
        </div>
        <div class="card-body">
            <form id="user-search-form" method="GET" action="{{ url_for('users.index') }}">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        {{ form.search.label(class="form-label") }}
                        {{ form.search(class="form-control", value=search) }}
                    </div>
                    <div class="col-md-4 mb-3">
                        {{ form.role.label(class="form-label") }}
                        {{ form.role(class="form-select", selected=role) }}
                    </div>
                    <div class="col-md-4 mb-3">
                        {{ form.status.label(class="form-label") }}
                        {{ form.status(class="form-select", selected=status) }}
                    </div>
                </div>
                <div class="text-center">
                    {{ form.submit(class="btn btn-primary") }}
                    <a href="{{ url_for('users.index') }}" class="btn btn-secondary">إعادة تعيين</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Users List -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-list"></i> قائمة المستخدمين</h5>
        </div>
        <div class="card-body">
            {% if users.items %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>اسم المستخدم</th>
                            <th>الاسم الكامل</th>
                            <th>البريد الإلكتروني</th>
                            <th>الصلاحية</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users.items %}
                        <tr>
                            <td>{{ user.username }}</td>
                            <td>{{ user.full_name or 'غير محدد' }}</td>
                            <td>{{ user.email }}</td>
                            <td>
                                {% if user.role.name == 'ADMIN' %}
                                <span class="badge bg-danger">{{ user.role.value }}</span>
                                {% elif user.role.name == 'SUPERVISOR' %}
                                <span class="badge bg-warning">{{ user.role.value }}</span>
                                {% else %}
                                <span class="badge bg-info">{{ user.role.value }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.status.name == 'ACTIVE' %}
                                <span class="badge bg-success">{{ user.status.value }}</span>
                                {% elif user.status.name == 'INACTIVE' %}
                                <span class="badge bg-secondary">{{ user.status.value }}</span>
                                {% else %}
                                <span class="badge bg-danger">{{ user.status.value }}</span>
                                {% endif %}
                            </td>
                            <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('users.view', id=user.id) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('users.edit', id=user.id) }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if user.id != current_user.id %}
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ user.id }}" data-bs-toggle="tooltip" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>

                                <!-- Delete Modal -->
                                <div class="modal fade" id="deleteModal{{ user.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ user.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ user.id }}">تأكيد الحذف</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                هل أنت متأكد من حذف المستخدم <strong>{{ user.username }}</strong>؟
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <form action="{{ url_for('users.delete', id=user.id) }}" method="POST">
                                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                    <button type="submit" class="btn btn-danger">حذف</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if users.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('users.index', page=users.prev_num, search=search, role=role, status=status) }}">السابق</a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">السابق</a>
                    </li>
                    {% endif %}

                    {% for page_num in users.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                        {% if page_num %}
                            {% if page_num == users.page %}
                            <li class="page-item active" aria-current="page">
                                <a class="page-link" href="{{ url_for('users.index', page=page_num, search=search, role=role, status=status) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('users.index', page=page_num, search=search, role=role, status=status) }}">{{ page_num }}</a>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#">...</a>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if users.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('users.index', page=users.next_num, search=search, role=role, status=status) }}">التالي</a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% else %}
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle fa-2x mb-3"></i>
                <p class="mb-0">لا توجد بيانات مستخدمين متطابقة مع معايير البحث.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
