#!/usr/bin/env python3
"""
Test runner script for HRM System
"""
import os
import sys
import subprocess
import argparse


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n{'='*50}")
    print(f"🔄 {description}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("Warnings:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        print(f"Output: {e.stdout}")
        print(f"Error: {e.stderr}")
        return False


def install_dependencies():
    """Install test dependencies."""
    dependencies = [
        'pytest>=7.4.3',
        'pytest-flask>=1.3.0',
        'pytest-cov>=4.1.0',
        'Flask-Limiter>=3.5.0',
        'bleach>=6.1.0'
    ]
    
    for dep in dependencies:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            return False
    return True


def run_tests(test_type='all', coverage=False, verbose=False):
    """Run tests based on type."""
    base_cmd = "python -m pytest"
    
    if verbose:
        base_cmd += " -v"
    
    if coverage:
        base_cmd += " --cov=hrmsystem --cov-report=html --cov-report=term"
    
    test_commands = {
        'all': f"{base_cmd} tests/",
        'auth': f"{base_cmd} tests/test_auth.py",
        'employees': f"{base_cmd} tests/test_employees.py",
        'leaves': f"{base_cmd} tests/test_leaves.py",
        'api': f"{base_cmd} tests/test_api.py",
        'security': f"{base_cmd} tests/test_security.py",
        'unit': f"{base_cmd} -m unit tests/",
        'integration': f"{base_cmd} -m integration tests/",
        'fast': f"{base_cmd} -m 'not slow' tests/"
    }
    
    if test_type not in test_commands:
        print(f"❌ Unknown test type: {test_type}")
        print(f"Available types: {', '.join(test_commands.keys())}")
        return False
    
    return run_command(test_commands[test_type], f"Running {test_type} tests")


def check_environment():
    """Check if the environment is set up correctly."""
    print("🔍 Checking environment...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        return False
    
    # Check if we're in the right directory
    if not os.path.exists('hrmsystem'):
        print("❌ Please run this script from the project root directory")
        return False
    
    # Check if virtual environment is activated (optional)
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Virtual environment not detected. Consider using a virtual environment.")
    
    print("✅ Environment check passed")
    return True


def generate_test_report():
    """Generate a comprehensive test report."""
    print("\n📊 Generating comprehensive test report...")
    
    # Run tests with coverage
    cmd = "python -m pytest tests/ --cov=hrmsystem --cov-report=html --cov-report=term --cov-report=xml --junit-xml=test-results.xml"
    
    if run_command(cmd, "Generating test report with coverage"):
        print("\n✅ Test report generated successfully!")
        print("📁 HTML coverage report: htmlcov/index.html")
        print("📁 XML coverage report: coverage.xml")
        print("📁 JUnit test results: test-results.xml")
        return True
    
    return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='HRM System Test Runner')
    parser.add_argument('--type', '-t', default='all',
                       choices=['all', 'auth', 'employees', 'leaves', 'api', 'security', 'unit', 'integration', 'fast'],
                       help='Type of tests to run')
    parser.add_argument('--coverage', '-c', action='store_true',
                       help='Run tests with coverage report')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Verbose output')
    parser.add_argument('--install-deps', '-i', action='store_true',
                       help='Install test dependencies')
    parser.add_argument('--report', '-r', action='store_true',
                       help='Generate comprehensive test report')
    
    args = parser.parse_args()
    
    print("🧪 HRM System Test Runner")
    print("=" * 50)
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    # Install dependencies if requested
    if args.install_deps:
        if not install_dependencies():
            print("❌ Failed to install dependencies")
            sys.exit(1)
    
    # Generate report if requested
    if args.report:
        if generate_test_report():
            sys.exit(0)
        else:
            sys.exit(1)
    
    # Run tests
    success = run_tests(args.type, args.coverage, args.verbose)
    
    if success:
        print("\n✅ All tests completed successfully!")
        print("\n📋 Next steps:")
        print("   • Review test results")
        print("   • Check coverage report (if generated)")
        print("   • Fix any failing tests")
        print("   • Add more tests for new features")
    else:
        print("\n❌ Some tests failed!")
        print("\n🔧 Troubleshooting:")
        print("   • Check error messages above")
        print("   • Ensure all dependencies are installed")
        print("   • Verify database setup")
        print("   • Run tests individually to isolate issues")
        sys.exit(1)


if __name__ == '__main__':
    main()
