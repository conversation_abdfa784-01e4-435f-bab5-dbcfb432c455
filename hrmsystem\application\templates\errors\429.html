{% extends "layouts/base.html" %}

{% block title %}كثرة الطلبات - 429{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="error-page text-center">
                <div class="error-code">
                    <h1 class="display-1 text-warning">429</h1>
                </div>
                <div class="error-message">
                    <h2 class="mb-3">كثرة الطلبات</h2>
                    <p class="lead text-muted mb-4">
                        عذراً، لقد تجاوزت الحد المسموح من الطلبات. يرجى الانتظار قليلاً قبل المحاولة مرة أخرى.
                    </p>
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-clock me-2"></i>
                        يرجى الانتظار بضع دقائق قبل المحاولة مرة أخرى.
                    </div>
                </div>
                <div class="error-actions">
                    <a href="{{ url_for('dashboard.index') }}" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-home me-2"></i>
                        العودة للرئيسية
                    </a>
                    <button onclick="setTimeout(function(){ location.reload(); }, 60000)" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-redo me-2"></i>
                        إعادة المحاولة بعد دقيقة
                    </button>
                </div>
                
                <!-- Countdown timer -->
                <div class="mt-4">
                    <p class="text-muted">
                        <i class="fas fa-hourglass-half me-2"></i>
                        يمكنك المحاولة مرة أخرى خلال: <span id="countdown">60</span> ثانية
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    padding: 60px 0;
}

.error-code h1 {
    font-size: 8rem;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.error-message h2 {
    color: #333;
    font-weight: 600;
}

.error-actions {
    margin-top: 30px;
}

@media (max-width: 768px) {
    .error-code h1 {
        font-size: 6rem;
    }
    
    .error-actions .btn {
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }
    
    .error-actions .me-3 {
        margin-right: 0 !important;
    }
}
</style>

<script>
// Countdown timer
let countdown = 60;
const countdownElement = document.getElementById('countdown');

const timer = setInterval(function() {
    countdown--;
    countdownElement.textContent = countdown;
    
    if (countdown <= 0) {
        clearInterval(timer);
        countdownElement.textContent = '0';
        // Enable retry button or auto-reload
        location.reload();
    }
}, 1000);
</script>
{% endblock %}
