from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from ..models import Employee, LeaveRequest, LeaveType, LeaveStatus, AuditLog
from .. import db
from .forms import LeaveRequestForm, LeaveTypeForm
from ..utilities import calculate_leave_days
from datetime import datetime, date
import calendar

leaves_bp = Blueprint('leaves', __name__)

@leaves_bp.route('/')
@login_required
def index():
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config['LEAVES_PER_PAGE']

    # Get filter parameters
    status = request.args.get('status', '')
    employee_id = request.args.get('employee_id', '')
    leave_type_id = request.args.get('leave_type_id', '')
    report = request.args.get('report', '')

    # Build query
    query = LeaveRequest.query.join(Employee).join(LeaveType)

    if status:
        query = query.filter(LeaveRequest.status == status)

    if employee_id:
        query = query.filter(LeaveRequest.employee_id == employee_id)

    if leave_type_id:
        query = query.filter(LeaveRequest.leave_type_id == leave_type_id)

    # Order by start_date (most recent first)
    query = query.order_by(LeaveRequest.start_date.desc())

    # Check if this is a report request
    if report:
        # Get all results without pagination for reports
        leaves_list = query.all()

        # Prepare report data based on report type
        if report == 'type':
            # Group leaves by type
            types = {}
            for leave in leaves_list:
                type_name = leave.leave_type_rel.name if leave.leave_type_rel else 'غير محدد'
                if type_name not in types:
                    types[type_name] = []
                types[type_name].append(leave)

            return render_template('reports/leave_type_report.html',
                                  report_type='type',
                                  report_data=types,
                                  now=datetime.now(),
                                  current_user=current_user,
                                  unit_name=current_app.config.get('DEPARTMENT_NAME', 'الخمس'),
                                  title='تقرير الإجازات حسب النوع')

    # Paginate results for normal view
    leaves = query.paginate(page=page, per_page=per_page)

    # Get all employees and leave types for filters
    employees = Employee.query.order_by(Employee.name).all()
    leave_types = LeaveType.query.order_by(LeaveType.name).all()

    return render_template('leaves/index.html',
                           leaves=leaves,
                           employees=employees,
                           leave_types=leave_types,
                           status=status,
                           employee_id=int(employee_id) if employee_id else None,
                           leave_type_id=int(leave_type_id) if leave_type_id else None,
                           title='إدارة الإجازات')

@leaves_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    form = LeaveRequestForm()

    # Populate employee choices
    employees = Employee.query.order_by(Employee.name).all()
    form.employee_id.choices = [(e.id, f"{e.name} ({e.military_id})") for e in employees]

    # Populate leave type choices
    leave_types = LeaveType.query.order_by(LeaveType.name).all()
    form.leave_type_id.choices = [(lt.id, lt.name) for lt in leave_types]

    if form.validate_on_submit():
        # Calculate total days
        total_days = calculate_leave_days(
            form.start_date.data,
            form.end_date.data,
            exclude_weekends=True,
            exclude_holidays=True
        )

        # Check if employee has enough leave balance
        employee = Employee.query.get(form.employee_id.data)
        if total_days > employee.leave_balance and form.deduct_from_balance.data:
            flash(f'رصيد الإجازة للموظف ({employee.leave_balance} يوم) غير كافٍ للإجازة المطلوبة ({total_days} يوم).', 'danger')
            return render_template('leaves/create.html', form=form, title='إضافة طلب إجازة جديد')

        # Create new leave request
        leave_request = LeaveRequest(
            employee_id=form.employee_id.data,
            leave_type_id=form.leave_type_id.data,
            start_date=form.start_date.data,
            end_date=form.end_date.data,
            total_days=total_days,
            reason=form.reason.data,
            status=LeaveStatus.APPROVED if form.auto_approve.data else LeaveStatus.PENDING
        )

        # If auto-approved, set approver and date
        if form.auto_approve.data:
            leave_request.approved_by = current_user.id
            leave_request.approved_at = datetime.now()

            # Deduct from leave balance if needed
            if form.deduct_from_balance.data:
                employee.leave_balance -= total_days

        db.session.add(leave_request)

        # Create audit log
        log = AuditLog(
            user_id=current_user.id,
            action='create',
            entity='LeaveRequest',
            entity_id=leave_request.id,
            details=f'تم إنشاء طلب إجازة للموظف {employee.name}',
            ip_address=request.remote_addr
        )
        db.session.add(log)

        db.session.commit()
        flash(f'تم إنشاء طلب الإجازة بنجاح!', 'success')
        return redirect(url_for('leaves.index'))

    return render_template('leaves/create.html', form=form, title='إضافة طلب إجازة جديد')

@leaves_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    leave_request = LeaveRequest.query.get_or_404(id)

    # Check if leave request is already approved or rejected
    if leave_request.status != LeaveStatus.PENDING:
        flash('لا يمكن تعديل طلب إجازة تمت الموافقة عليه أو رفضه.', 'warning')
        return redirect(url_for('leaves.view', id=leave_request.id))

    form = LeaveRequestForm(obj=leave_request)

    # Populate employee choices
    employees = Employee.query.order_by(Employee.name).all()
    form.employee_id.choices = [(e.id, f"{e.name} ({e.military_id})") for e in employees]

    # Populate leave type choices
    leave_types = LeaveType.query.order_by(LeaveType.name).all()
    form.leave_type_id.choices = [(lt.id, lt.name) for lt in leave_types]

    if form.validate_on_submit():
        # Calculate total days
        total_days = calculate_leave_days(
            form.start_date.data,
            form.end_date.data,
            exclude_weekends=True,
            exclude_holidays=True
        )

        # Check if employee has enough leave balance
        employee = Employee.query.get(form.employee_id.data)
        if total_days > employee.leave_balance and form.deduct_from_balance.data:
            flash(f'رصيد الإجازة للموظف ({employee.leave_balance} يوم) غير كافٍ للإجازة المطلوبة ({total_days} يوم).', 'danger')
            return render_template('leaves/edit.html', form=form, leave=leave_request, title='تعديل طلب إجازة')

        # Update leave request
        leave_request.employee_id = form.employee_id.data
        leave_request.leave_type_id = form.leave_type_id.data
        leave_request.start_date = form.start_date.data
        leave_request.end_date = form.end_date.data
        leave_request.total_days = total_days
        leave_request.reason = form.reason.data

        # If auto-approved, set status, approver and date
        if form.auto_approve.data and leave_request.status == LeaveStatus.PENDING:
            leave_request.status = LeaveStatus.APPROVED
            leave_request.approved_by = current_user.id
            leave_request.approved_at = datetime.now()

            # Deduct from leave balance if needed
            if form.deduct_from_balance.data:
                employee.leave_balance -= total_days

        # Create audit log
        log = AuditLog(
            user_id=current_user.id,
            action='update',
            entity='LeaveRequest',
            entity_id=leave_request.id,
            details=f'تم تحديث طلب إجازة للموظف {employee.name}',
            ip_address=request.remote_addr
        )
        db.session.add(log)

        db.session.commit()
        flash(f'تم تحديث طلب الإجازة بنجاح!', 'success')
        return redirect(url_for('leaves.view', id=leave_request.id))

    return render_template('leaves/edit.html', form=form, leave=leave_request, title='تعديل طلب إجازة')

@leaves_bp.route('/view/<int:id>')
@login_required
def view(id):
    leave_request = LeaveRequest.query.get_or_404(id)
    return render_template('leaves/view.html', leave=leave_request, title=f'عرض طلب إجازة')

@leaves_bp.route('/approve/<int:id>', methods=['POST'])
@login_required
def approve(id):
    leave_request = LeaveRequest.query.get_or_404(id)

    # Check if leave request is already approved or rejected
    if leave_request.status != LeaveStatus.PENDING:
        flash('تم بالفعل الموافقة على طلب الإجازة أو رفضه.', 'warning')
        return redirect(url_for('leaves.view', id=leave_request.id))

    # Get deduct_from_balance parameter
    deduct_from_balance = request.form.get('deduct_from_balance') == 'true'

    # Approve leave request
    leave_request.status = LeaveStatus.APPROVED
    leave_request.approved_by = current_user.id
    leave_request.approved_at = datetime.now()

    # Deduct from leave balance if needed
    if deduct_from_balance:
        employee = leave_request.employee
        if leave_request.total_days > employee.leave_balance:
            flash(f'رصيد الإجازة للموظف ({employee.leave_balance} يوم) غير كافٍ للإجازة المطلوبة ({leave_request.total_days} يوم).', 'danger')
            return redirect(url_for('leaves.view', id=leave_request.id))

        employee.leave_balance -= leave_request.total_days

    # Create audit log
    log = AuditLog(
        user_id=current_user.id,
        action='approve',
        entity='LeaveRequest',
        entity_id=leave_request.id,
        details=f'تمت الموافقة على طلب إجازة للموظف {leave_request.employee.name}',
        ip_address=request.remote_addr
    )
    db.session.add(log)

    db.session.commit()
    flash(f'تمت الموافقة على طلب الإجازة بنجاح!', 'success')
    return redirect(url_for('leaves.view', id=leave_request.id))

@leaves_bp.route('/reject/<int:id>', methods=['POST'])
@login_required
def reject(id):
    leave_request = LeaveRequest.query.get_or_404(id)

    # Check if leave request is already approved or rejected
    if leave_request.status != LeaveStatus.PENDING:
        flash('تم بالفعل الموافقة على طلب الإجازة أو رفضه.', 'warning')
        return redirect(url_for('leaves.view', id=leave_request.id))

    # Get rejection reason
    rejection_reason = request.form.get('rejection_reason', '')

    # Reject leave request
    leave_request.status = LeaveStatus.REJECTED
    leave_request.approved_by = current_user.id
    leave_request.approved_at = datetime.now()
    leave_request.rejection_reason = rejection_reason

    # Create audit log
    log = AuditLog(
        user_id=current_user.id,
        action='reject',
        entity='LeaveRequest',
        entity_id=leave_request.id,
        details=f'تم رفض طلب إجازة للموظف {leave_request.employee.name}',
        ip_address=request.remote_addr
    )
    db.session.add(log)

    db.session.commit()
    flash(f'تم رفض طلب الإجازة بنجاح!', 'success')
    return redirect(url_for('leaves.view', id=leave_request.id))

@leaves_bp.route('/cancel/<int:id>', methods=['POST'])
@login_required
def cancel(id):
    leave_request = LeaveRequest.query.get_or_404(id)

    # Check if leave request is already cancelled
    if leave_request.status == LeaveStatus.CANCELLED:
        flash('تم بالفعل إلغاء طلب الإجازة.', 'warning')
        return redirect(url_for('leaves.view', id=leave_request.id))

    # If leave was approved and balance was deducted, restore balance
    if leave_request.status == LeaveStatus.APPROVED:
        # Get restore_balance parameter
        restore_balance = request.form.get('restore_balance') == 'true'

        if restore_balance:
            employee = leave_request.employee
            employee.leave_balance += leave_request.total_days

    # Cancel leave request
    leave_request.status = LeaveStatus.CANCELLED

    # Create audit log
    log = AuditLog(
        user_id=current_user.id,
        action='cancel',
        entity='LeaveRequest',
        entity_id=leave_request.id,
        details=f'تم إلغاء طلب إجازة للموظف {leave_request.employee.name}',
        ip_address=request.remote_addr
    )
    db.session.add(log)

    db.session.commit()
    flash(f'تم إلغاء طلب الإجازة بنجاح!', 'success')
    return redirect(url_for('leaves.view', id=leave_request.id))

@leaves_bp.route('/delete/<int:id>', methods=['POST'])
@login_required
def delete(id):
    leave_request = LeaveRequest.query.get_or_404(id)

    # Create audit log
    log = AuditLog(
        user_id=current_user.id,
        action='delete',
        entity='LeaveRequest',
        entity_id=leave_request.id,
        details=f'تم حذف طلب إجازة للموظف {leave_request.employee.name}',
        ip_address=request.remote_addr
    )
    db.session.add(log)

    db.session.delete(leave_request)
    db.session.commit()
    flash(f'تم حذف طلب الإجازة بنجاح!', 'success')
    return redirect(url_for('leaves.index'))

@leaves_bp.route('/types')
@login_required
def types():
    leave_types = LeaveType.query.order_by(LeaveType.name).all()
    return render_template('leaves/types.html', leave_types=leave_types, title='أنواع الإجازات')

@leaves_bp.route('/types/create', methods=['GET', 'POST'])
@login_required
def create_type():
    form = LeaveTypeForm()

    if form.validate_on_submit():
        leave_type = LeaveType(
            name=form.name.data,
            description=form.description.data,
            color=form.color.data
        )

        db.session.add(leave_type)

        # Create audit log
        log = AuditLog(
            user_id=current_user.id,
            action='create',
            entity='LeaveType',
            entity_id=leave_type.id,
            details=f'تم إنشاء نوع إجازة جديد: {leave_type.name}',
            ip_address=request.remote_addr
        )
        db.session.add(log)

        db.session.commit()
        flash(f'تم إنشاء نوع الإجازة بنجاح!', 'success')
        return redirect(url_for('leaves.types'))

    return render_template('leaves/create_type.html', form=form, title='إضافة نوع إجازة جديد')

@leaves_bp.route('/types/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_type(id):
    leave_type = LeaveType.query.get_or_404(id)
    form = LeaveTypeForm(obj=leave_type)

    if form.validate_on_submit():
        leave_type.name = form.name.data
        leave_type.description = form.description.data
        leave_type.color = form.color.data

        # Create audit log
        log = AuditLog(
            user_id=current_user.id,
            action='update',
            entity='LeaveType',
            entity_id=leave_type.id,
            details=f'تم تحديث نوع الإجازة: {leave_type.name}',
            ip_address=request.remote_addr
        )
        db.session.add(log)

        db.session.commit()
        flash(f'تم تحديث نوع الإجازة بنجاح!', 'success')
        return redirect(url_for('leaves.types'))

    return render_template('leaves/edit_type.html', form=form, leave_type=leave_type, title='تعديل نوع إجازة')

@leaves_bp.route('/types/delete/<int:id>', methods=['POST'])
@login_required
def delete_type(id):
    leave_type = LeaveType.query.get_or_404(id)

    # Check if leave type is used in any leave request
    if leave_type.leave_requests:
        flash(f'لا يمكن حذف نوع الإجازة لأنه مستخدم في طلبات إجازة.', 'danger')
        return redirect(url_for('leaves.types'))

    # Create audit log
    log = AuditLog(
        user_id=current_user.id,
        action='delete',
        entity='LeaveType',
        entity_id=leave_type.id,
        details=f'تم حذف نوع الإجازة: {leave_type.name}',
        ip_address=request.remote_addr
    )
    db.session.add(log)

    db.session.delete(leave_type)
    db.session.commit()
    flash(f'تم حذف نوع الإجازة بنجاح!', 'success')
    return redirect(url_for('leaves.types'))

@leaves_bp.route('/calendar')
@login_required
def calendar():
    # Get all approved leave requests
    leaves = LeaveRequest.query.filter_by(status=LeaveStatus.APPROVED).all()

    # Format leave data for calendar
    events = []
    for leave in leaves:
        events.append({
            'id': leave.id,
            'title': f'{leave.employee.name} - {leave.leave_type_rel.name}',
            'start': leave.start_date.isoformat(),
            'end': leave.end_date.isoformat(),
            'color': leave.leave_type_rel.color,
            'url': url_for('leaves.view', id=leave.id)
        })

    return render_template('leaves/calendar.html', events=events, title='تقويم الإجازات')

@leaves_bp.route('/calculate_days')
@login_required
def calculate_days():
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')

    if not start_date_str or not end_date_str:
        return jsonify({'days': 0})

    try:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()

        days = calculate_leave_days(start_date, end_date)
        return jsonify({'days': days})
    except:
        return jsonify({'days': 0})

@leaves_bp.route('/employee_balance/<int:employee_id>')
@login_required
def employee_balance(employee_id):
    employee = Employee.query.get_or_404(employee_id)
    return jsonify({'balance': employee.leave_balance})

@leaves_bp.route('/export_report')
@login_required
def export_report():
    """Export leave reports to Excel"""
    report_type = request.args.get('report_type', '')

    # Get filter parameters
    status = request.args.get('status', '')
    employee_id = request.args.get('employee_id', '')
    leave_type_id = request.args.get('leave_type_id', '')

    # Build query
    query = LeaveRequest.query.join(Employee).join(LeaveType)

    if status:
        query = query.filter(LeaveRequest.status == status)

    if employee_id:
        query = query.filter(LeaveRequest.employee_id == employee_id)

    if leave_type_id:
        query = query.filter(LeaveRequest.leave_type_id == leave_type_id)

    # Order by start_date (most recent first)
    query = query.order_by(LeaveRequest.start_date.desc())

    # Get all results without pagination for reports
    leaves_list = query.all()

    # Create a pandas DataFrame for the Excel file
    data = []

    if report_type == 'type':
        # Group leaves by type
        types = {}
        for leave in leaves_list:
            type_name = leave.leave_type_rel.name if leave.leave_type_rel else 'غير محدد'
            if type_name not in types:
                types[type_name] = []
            types[type_name].append(leave)

        # Add data to the DataFrame
        counter = 1
        for type_name, leaves in types.items():
            # Add a group header row
            data.append({
                'م': '',
                'الرقم العسكري': '',
                'الرتبة': '',
                'الاسم': type_name,
                'من تاريخ': '',
                'إلى تاريخ': '',
                'الأيام': '',
            })

            # Add leave rows
            for leave in leaves:
                data.append({
                    'م': counter,
                    'الرقم العسكري': leave.employee.military_id,
                    'الرتبة': leave.employee.military_rank,
                    'الاسم': leave.employee.name,
                    'من تاريخ': leave.start_date.strftime('%Y/%m/%d'),
                    'إلى تاريخ': leave.end_date.strftime('%Y/%m/%d'),
                    'الأيام': leave.total_days,
                })
                counter += 1

    # Create DataFrame
    import pandas as pd
    import io
    from flask import send_file

    df = pd.DataFrame(data)

    # Create Excel file in memory
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='تقرير الإجازات')

        # Auto-adjust columns' width
        worksheet = writer.sheets['تقرير الإجازات']
        for i, col in enumerate(df.columns):
            max_length = max(df[col].astype(str).map(len).max(), len(col)) + 2
            worksheet.column_dimensions[chr(65 + i)].width = max_length

    output.seek(0)

    # Generate a unique filename
    filename = f"report_leaves_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"

    return send_file(
        output,
        as_attachment=True,
        download_name=filename,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
