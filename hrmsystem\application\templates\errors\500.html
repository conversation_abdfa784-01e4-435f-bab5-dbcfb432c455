{% extends "layouts/base.html" %}

{% block title %}خطأ في الخادم - 500{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="error-page text-center">
                <div class="error-code">
                    <h1 class="display-1 text-danger">500</h1>
                </div>
                <div class="error-message">
                    <h2 class="mb-3">خطأ في الخادم</h2>
                    <p class="lead text-muted mb-4">
                        عذراً، حدث خطأ غير متوقع في الخادم. يرجى المحاولة مرة أخرى لاحقاً.
                    </p>
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        إذا استمر هذا الخطأ، يرجى الاتصال بمدير النظام.
                    </div>
                </div>
                <div class="error-actions">
                    <a href="{{ url_for('dashboard.index') }}" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-home me-2"></i>
                        العودة للرئيسية
                    </a>
                    <button onclick="location.reload()" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-redo me-2"></i>
                        إعادة المحاولة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    padding: 60px 0;
}

.error-code h1 {
    font-size: 8rem;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.error-message h2 {
    color: #333;
    font-weight: 600;
}

.error-actions {
    margin-top: 30px;
}

@media (max-width: 768px) {
    .error-code h1 {
        font-size: 6rem;
    }
    
    .error-actions .btn {
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }
    
    .error-actions .me-3 {
        margin-right: 0 !important;
    }
}
</style>
{% endblock %}
