# نظام إدارة الموارد البشرية (HRM)

نظام إدارة الموارد البشرية هو تطبيق ويب مصمم لإدارة موظفي المؤسسة العسكرية، بما في ذلك معلوماتهم الشخصية، طلبات الإجازة، والإعدادات النظامية.

## المميزات

- إدارة بيانات الموظفين (إضافة، تعديل، حذف، عرض)
- إدارة طلبات الإجازات (تقديم، موافقة، رفض)
- إدارة المستخدمين والصلاحيات
- واجهة مستخدم باللغة العربية
- تصميم متجاوب يعمل على جميع الأجهزة
- لوحة تحكم تفاعلية مع رسوم بيانية

## المتطلبات

- Python 3.8+
- Flask 2.0+
- MySQL 5.7+
- مكتبات Python الإضافية:
  - Flask-SQLAlchemy
  - Flask-Login
  - Flask-WTF
  - PyMySQL

## التثبيت

1. قم بنسخ المستودع:

```bash
git clone https://github.com/yourusername/hrm-system.git
cd hrm-system
```

2. قم بإنشاء بيئة افتراضية وتفعيلها:

```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

3. قم بتثبيت المكتبات المطلوبة:

```bash
pip install -r hrmsystem/requirements.txt
```

## 🧪 تشغيل الاختبارات

### التثبيت السريع للاختبارات:
```bash
python run_tests.py --install-deps
```

### تشغيل جميع الاختبارات:
```bash
python run_tests.py
```

### تشغيل اختبارات محددة:
```bash
# اختبارات المصادقة
python run_tests.py --type auth

# اختبارات الموظفين
python run_tests.py --type employees

# اختبارات API
python run_tests.py --type api

# اختبارات الأمان
python run_tests.py --type security
```

### تشغيل الاختبارات مع تقرير التغطية:
```bash
python run_tests.py --coverage --report
```

4. قم بتعديل ملف `config.py` لإعداد اتصال قاعدة البيانات:

```python
SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://username:password@localhost/hrm_db'
```

5. قم بإنشاء قاعدة البيانات وتهيئتها:

```bash
# إنشاء قاعدة البيانات في MySQL
mysql -u root -p
CREATE DATABASE hrm_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
exit;

# تهيئة قاعدة البيانات بالبيانات الأولية
python init_db.py
```

6. قم بتشغيل التطبيق:

```bash
python run.py
```

7. افتح المتصفح على العنوان: `http://localhost:5000`

## 🔐 بيانات تسجيل الدخول الافتراضية

- البريد الإلكتروني: <EMAIL>
- كلمة المرور: admin123

## 🚀 API Documentation

يوفر النظام API RESTful للتكامل مع التطبيقات الأخرى.

### المصادقة
```bash
# الحصول على token
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

### استخدام API
```bash
# الحصول على قائمة الموظفين
curl -X GET http://localhost:5000/api/employees \
  -H "Authorization: Bearer YOUR_TOKEN"

# إضافة موظف جديد
curl -X POST http://localhost:5000/api/employees \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"military_id": "12345", "name": "أحمد محمد"}'
```

### Endpoints المتاحة:
- `POST /api/auth/login` - تسجيل الدخول
- `GET /api/employees` - قائمة الموظفين
- `POST /api/employees` - إضافة موظف
- `GET /api/employees/{id}` - تفاصيل موظف
- `GET /api/leaves` - قائمة الإجازات
- `POST /api/leaves/{id}/approve` - الموافقة على إجازة
- `POST /api/leaves/{id}/reject` - رفض إجازة
- `GET /api/stats/dashboard` - إحصائيات النظام

## 🛡️ الأمان والحماية

### Rate Limiting
- حماية من الهجمات بتحديد عدد الطلبات
- 5 محاولات تسجيل دخول كل 15 دقيقة
- 1000 طلب API كل ساعة

### التحقق من البيانات
- تنظيف المدخلات من XSS
- التحقق من صحة البيانات
- تشفير كلمات المرور

### السجلات والمراقبة
- تسجيل جميع العمليات الأمنية
- مراقبة محاولات الوصول غير المصرح
- سجلات التدقيق للتغييرات

## هيكل المشروع

```
/hrmsystem
│── /application
│   ├── __init__.py
│   ├── auth/
│   │   ├── routes.py
│   │   └── forms.py
│   ├── employees/
│   │   ├── routes.py
│   │   └── forms.py
│   ├── leaves/
│   │   ├── routes.py
│   │   └── forms.py
│   ├── static/
│   │   ├── css/
│   │   │   ├── auth.css
│   │   │   ├── main.css
│   │   │   └── bootstrap.min.css
│   │   ├── js/
│   │   │   ├── auth.js
│   │   │   └── main.js
│   │   └── img/
│   │       ├── logo.png
│   │       └── bg-auth.jpg
│   ├── templates/
│   │   ├── auth/
│   │   │   ├── login.html
│   │   │   └── register.html
│   │   ├── employees/
│   │   ├── leaves/
│   │   ├── layouts/
│   │   │   ├── base.html
│   │   │   └── auth_base.html
│   │   └── dashboard.html
│   ├── models.py
│   └── utilities.py
│── config.py
│── requirements.txt
│── run.py
```

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. قم بعمل Fork للمشروع
2. قم بإنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. قم بعمل Commit للتغييرات (`git commit -m 'Add some amazing feature'`)
4. قم بدفع التغييرات إلى الفرع (`git push origin feature/amazing-feature`)
5. قم بفتح طلب Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## الاتصال

اسمك - [@your_twitter](https://twitter.com/your_twitter) - <EMAIL>

رابط المشروع: [https://github.com/yourusername/hrm-system](https://github.com/yourusername/hrm-system)
