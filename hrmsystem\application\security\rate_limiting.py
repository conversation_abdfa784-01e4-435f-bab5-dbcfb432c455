"""
Rate limiting utilities for HRM System
"""
from flask import request, jsonify, current_app
from functools import wraps
import time
from collections import defaultdict, deque
import threading


class RateLimiter:
    """Simple in-memory rate limiter."""
    
    def __init__(self):
        self.requests = defaultdict(deque)
        self.lock = threading.Lock()
    
    def is_allowed(self, key, limit, window):
        """Check if request is allowed based on rate limit."""
        now = time.time()
        
        with self.lock:
            # Clean old requests outside the window
            while self.requests[key] and self.requests[key][0] <= now - window:
                self.requests[key].popleft()
            
            # Check if limit exceeded
            if len(self.requests[key]) >= limit:
                return False
            
            # Add current request
            self.requests[key].append(now)
            return True


# Global rate limiter instance
rate_limiter = RateLimiter()


def limit_requests(limit=100, window=3600, per='ip'):
    """
    Decorator to limit requests per IP or user.
    
    Args:
        limit: Number of requests allowed
        window: Time window in seconds
        per: 'ip' or 'user' - what to limit by
    """
    def decorator(f):
        @wraps(f)
        def decorated(*args, **kwargs):
            # Determine the key to limit by
            if per == 'ip':
                key = f"ip:{request.remote_addr}"
            elif per == 'user':
                from flask_login import current_user
                if current_user.is_authenticated:
                    key = f"user:{current_user.id}"
                else:
                    key = f"ip:{request.remote_addr}"
            else:
                key = f"ip:{request.remote_addr}"
            
            # Check rate limit
            if not rate_limiter.is_allowed(key, limit, window):
                if request.is_json:
                    return jsonify({
                        'error': 'Rate limit exceeded',
                        'message': f'Too many requests. Limit: {limit} per {window} seconds'
                    }), 429
                else:
                    return render_template('errors/429.html'), 429
            
            return f(*args, **kwargs)
        return decorated
    return decorator


def login_rate_limit(f):
    """Special rate limiter for login attempts."""
    @wraps(f)
    def decorated(*args, **kwargs):
        ip = request.remote_addr
        key = f"login:{ip}"
        
        # Allow 5 login attempts per 15 minutes
        if not rate_limiter.is_allowed(key, 5, 900):
            if request.is_json:
                return jsonify({
                    'error': 'Too many login attempts',
                    'message': 'Please wait 15 minutes before trying again'
                }), 429
            else:
                from flask import flash, render_template
                flash('تم تجاوز عدد محاولات تسجيل الدخول المسموح. يرجى المحاولة بعد 15 دقيقة.', 'error')
                return render_template('auth/login.html'), 429
        
        return f(*args, **kwargs)
    return decorated


def api_rate_limit(f):
    """Rate limiter for API endpoints."""
    @wraps(f)
    def decorated(*args, **kwargs):
        # Get API key or use IP
        api_key = request.headers.get('X-API-Key')
        if api_key:
            key = f"api:{api_key}"
        else:
            key = f"api_ip:{request.remote_addr}"
        
        # Allow 1000 API requests per hour
        if not rate_limiter.is_allowed(key, 1000, 3600):
            return jsonify({
                'error': 'API rate limit exceeded',
                'message': 'Too many API requests. Limit: 1000 per hour'
            }), 429
        
        return f(*args, **kwargs)
    return decorated
