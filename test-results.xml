<?xml version="1.0" encoding="utf-8"?><testsuites name="pytest tests"><testsuite name="pytest" errors="1" failures="0" skipped="0" tests="1" time="0.744" timestamp="2025-06-13T15:40:02.824851+02:00" hostname="DESKTOP-DHB2V0S"><testcase classname="" name="tests.test_security" time="0.000"><error message="collection failure">ImportError while importing test module 'C:\Users\<USER>\Desktop\برمجة\nhrm\tests\test_security.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
C:\Program Files\Python311\Lib\importlib\__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests\test_security.py:6: in &lt;module&gt;
    from hrmsystem.application.security.validation import (
hrmsystem\application\security\validation.py:7: in &lt;module&gt;
    import bleach
E   ModuleNotFoundError: No module named 'bleach'</error></testcase></testsuite></testsuites>